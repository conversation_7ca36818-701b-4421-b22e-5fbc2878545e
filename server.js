const express = require('express');
const mysql = require('mysql2');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const wechatApi = require('./wechat-api'); // 引入微信API模块

/**
 * 将HTML内容解析为段落数组（按<p>标签严格分段）
 * @param {string} html HTML内容
 * @returns {Array} 段落数组
 */
function parseHtmlToSections(html) {
    if (!html || !html.trim()) return [];

    const sections = [];
    let sectionOrder = 0;

    // 首先移除标题，因为标题不需要分段保存
    let processedHtml = html.replace(/<h[1-6][^>]*>.*?<\/h[1-6]>/gi, '');

    // 新的分段策略：严格按照<p>标签分段
    // 每个<p>...</p>都作为独立的一个段落

    // 1. 先提取所有的<p>标签内容
    const paragraphMatches = processedHtml.match(/<p[^>]*>.*?<\/p>/gi) || [];

    paragraphMatches.forEach(paragraph => {
        // 提取图片URL
        const imageUrls = [];
        const imgMatches = paragraph.match(/<img[^>]+src=["']([^"']+)["'][^>]*>/gi);
        if (imgMatches) {
            imgMatches.forEach(imgTag => {
                const srcMatch = imgTag.match(/src=["']([^"']+)["']/i);
                if (srcMatch) {
                    imageUrls.push(srcMatch[1]);
                }
            });
        }

        // 提取纯文本内容（移除HTML标签）
        let textContent = paragraph.replace(/<img[^>]*>/gi, ''); // 先移除图片标签
        textContent = textContent.replace(/<[^>]*>/g, ''); // 移除其他HTML标签
        textContent = textContent.replace(/&nbsp;/g, ' '); // 替换&nbsp;
        textContent = textContent.replace(/&mdash;/g, '—'); // 替换&mdash;
        textContent = textContent.trim();

        // 只有当有文本内容或图片时才创建段落
        if (textContent || imageUrls.length > 0) {
            sections.push({
                section_order: sectionOrder++,
                content_text: textContent,
                html_content: paragraph, // 保存原始HTML内容，包含样式
                image_urls: imageUrls
            });
        }
    });

    // 2. 处理不在<p>标签内的独立图片
    const remainingHtml = processedHtml.replace(/<p[^>]*>.*?<\/p>/gi, '');
    const standaloneImages = remainingHtml.match(/<img[^>]+src=["']([^"']+)["'][^>]*>/gi);
    if (standaloneImages) {
        standaloneImages.forEach(imgTag => {
            const srcMatch = imgTag.match(/src=["']([^"']+)["']/i);
            if (srcMatch) {
                sections.push({
                    section_order: sectionOrder++,
                    content_text: '',
                    html_content: imgTag, // 保存图片的HTML标签
                    image_urls: [srcMatch[1]]
                });
            }
        });
    }

    // 3. 如果没有找到任何<p>标签，作为单段处理
    if (sections.length === 0) {
        const imageUrls = [];
        const imgMatches = processedHtml.match(/<img[^>]+src=["']([^"']+)["'][^>]*>/gi);
        if (imgMatches) {
            imgMatches.forEach(imgTag => {
                const srcMatch = imgTag.match(/src=["']([^"']+)["']/i);
                if (srcMatch) {
                    imageUrls.push(srcMatch[1]);
                }
            });
        }

        const textContent = processedHtml.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').replace(/&mdash;/g, '—').trim();

        if (textContent || imageUrls.length > 0) {
            sections.push({
                section_order: 0,
                content_text: textContent,
                html_content: processedHtml, // 保存完整的HTML内容
                image_urls: imageUrls
            });
        }
    }

    return sections;
}



// 在文件顶部保留服务器初始化日志
console.log('服务器初始化开始...');

const app = express();
const port = 3002;

// 中间件配置
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 添加请求日志中间件
app.use((req, res, next) => {
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
    next();
});

// 日志记录中间件
app.use((req, res, next) => {
    // 删除请求日志
    next();
});

// 先配置静态文件服务
app.use(express.static('public'));
app.use('/uploads', express.static(path.join(__dirname, 'public/uploads')));
app.use('/images', express.static(path.join(__dirname, 'public/images')));
app.use('/login', express.static(path.join(__dirname, 'login')));
app.use('/admin/media', express.static(path.join(__dirname, 'admin/media')));  // 确保admin/media目录可以访问
app.use('/media', express.static(path.join(__dirname, 'admin/media')));
app.use('/static', express.static(path.join(__dirname, 'public/static')));

// 添加英文版路径支持
app.use('/en', express.static(path.join(__dirname, 'public')));

app.get('/en/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public/index_en.html'));
});

// 配置admin静态文件目录
app.use('/admin/css', express.static(path.join(__dirname, 'admin/css')));
app.use('/admin/js', express.static(path.join(__dirname, 'admin/js')));
app.use('/admin/font-awesome', express.static(path.join(__dirname, 'admin/font-awesome')));
app.use('/admin/img', express.static(path.join(__dirname, 'admin/img')));
app.use('/admin/i18n', express.static(path.join(__dirname, 'admin/i18n')));
app.use('/admin/fonts', express.static(path.join(__dirname, 'admin/font-awesome/fonts')));
app.use('/admin/uploads', express.static(path.join(__dirname, 'admin/uploads')));

// 配置h5静态文件目录
app.use('/h5/css', express.static(path.join(__dirname, 'h5/css')));
app.use('/h5/js', express.static(path.join(__dirname, 'h5/js')));
app.use('/h5/font-awesome', express.static(path.join(__dirname, 'h5/font-awesome')));
app.use('/h5/img', express.static(path.join(__dirname, 'h5/img')));
app.use('/h5/fonts', express.static(path.join(__dirname, 'h5/font-awesome/fonts')));

// 最后配置目录，确保特定路由优先级更高
app.use('/admin', express.static(path.join(__dirname, 'admin')));
app.use('/h5', express.static(path.join(__dirname, 'h5')));

// 添加特定路由处理，支持访问admin_home.html
app.get('/admin/admin_home.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'admin/admin_home.html'));
});

// 添加admin1的路由处理
app.get('/admin1/admin_home.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'admin1/admin_home.html'));
});

// 添加重定向，将/admin1/home.html重定向到/admin1/admin_home.html
app.get('/admin1/home.html', (req, res) => {
    res.redirect('/admin1/admin_home.html');
});

// 添加管理后台首页路由
app.get('/admin1/', (req, res) => {
    res.redirect('/admin1/admin_home.html');
});

app.get('/admin1', (req, res) => {
    res.redirect('/admin1/admin_home.html');
});

// 添加重定向，将/admin/home.html重定向到/admin/admin_home.html
app.get('/admin/home.html', (req, res) => {
    res.redirect('/admin/admin_home.html');
});

// 添加管理后台首页路由
app.get('/admin/', (req, res) => {
    res.redirect('/admin/admin_home.html');
});

app.get('/admin', (req, res) => {
    res.redirect('/admin/admin_home.html');
});

// 添加额外的管理页面路由
app.get('/admin/index.html', (req, res) => {
    res.redirect('/admin/admin_home.html');
});

// 确保所有管理页面都能正确加载
app.get('/admin/:page', (req, res, next) => {
    const page = req.params.page;
    const filePath = path.join(__dirname, 'admin', page);
    
    // 检查文件是否存在
    if (fs.existsSync(filePath)) {
        res.sendFile(filePath);
    } else {
        next(); // 继续下一个路由处理器
    }
});

// 添加刷新轮播图缓存的API端点
app.get('/refresh-carousel-cache', (req, res) => {
    // 设置SSE响应头
    res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
    });
    
    // 发送初始消息
    res.write('data: ' + JSON.stringify({status: 'connected'}) + '\n\n');
    
    // 保存客户端连接
    const clientId = Date.now();
    const clients = app.locals.clients = app.locals.clients || {};
    clients[clientId] = res;
    
    // 当连接关闭时清理
    req.on('close', () => {
        delete clients[clientId];
    });
});

// 添加通知所有客户端刷新轮播图的函数
function notifyCarouselRefresh() {
    const clients = app.locals.clients || {};
    const clientIds = Object.keys(clients);
    
    if (clientIds.length > 0) {
        const message = JSON.stringify({
            status: 'refresh',
            timestamp: new Date().toISOString()
        });
        
        for (const clientId of clientIds) {
            const client = clients[clientId];
            client.write(`data: ${message}\n\n`);
        }
    }
}

// 添加特定路由处理，支持访问admin目录下的CSS和JS文件
app.get('/admin/css/:file', (req, res) => {
    res.sendFile(path.join(__dirname, 'admin/css', req.params.file));
});

app.get('/admin/js/:file', (req, res) => {
    res.sendFile(path.join(__dirname, 'admin/js', req.params.file));
});

// 添加字体文件路由处理
app.get('/admin/fonts/:file', (req, res) => {
    // 首先尝试从admin/font-awesome/fonts目录获取
    const fontPath = path.join(__dirname, 'admin/font-awesome/fonts', req.params.file);
    if (fs.existsSync(fontPath)) {
        res.sendFile(fontPath);
    } else {
        res.status(404).send('Font not found');
    }
});

// 确保上传目录存在
const uploadDir = path.join(__dirname, 'public/uploads');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// 确保图片目录存在
const imagesDir = path.join(__dirname, 'public/images');
if (!fs.existsSync(imagesDir)) {
    fs.mkdirSync(imagesDir, { recursive: true });
}

// 确保admin/media目录存在（用于备份上传的图片）
const mediaBackupDir = path.join(__dirname, 'admin/media');
if (!fs.existsSync(mediaBackupDir)) {
    fs.mkdirSync(mediaBackupDir, { recursive: true });
}

// 确保public/static/image/index目录存在
const staticImageDir = path.join(__dirname, 'public/static/image/index');
if (!fs.existsSync(staticImageDir)) {
    fs.mkdirSync(staticImageDir, { recursive: true });
}

// 创建图片备份日志文件
const mediaLogFile = path.join(__dirname, 'admin/media/upload_log.txt');
if (!fs.existsSync(mediaLogFile)) {
    fs.writeFileSync(mediaLogFile, '图片上传日志\n格式：[时间] [操作类型] [文件名] [路径]\n\n', 'utf8');
}

// 记录图片操作日志的函数（简化版）
function logMediaOperation(operation, filename, filePath) {
    const now = new Date();
    const logEntry = `[${now.toISOString()}] [${operation}] [${filename}] [${filePath}]\n`;
    
    try {
        fs.appendFileSync(mediaLogFile, logEntry, 'utf8');
    } catch (err) {
        // 记录失败，忽略错误
    }
}

// 简化的备份媒体文件函数
function backupMedia(sourceFile, originalFilename) {
    try {
        // 直接复制到media目录，不创建子目录
        const backupPath = path.join(mediaBackupDir, originalFilename);
        
        // 如果文件已存在，添加时间戳前缀避免覆盖
        if (fs.existsSync(backupPath)) {
            const timestamp = Date.now();
            const newFilename = `${timestamp}_${originalFilename}`;
            const newBackupPath = path.join(mediaBackupDir, newFilename);
            fs.copyFileSync(sourceFile, newBackupPath);
            logMediaOperation('备份', newFilename, newBackupPath);
            return newBackupPath;
        } else {
            // 直接复制
            fs.copyFileSync(sourceFile, backupPath);
            logMediaOperation('备份', originalFilename, backupPath);
            return backupPath;
        }
    } catch (err) {
        return null;
    }
}

// 配置文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '_' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + '_' + file.originalname);
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 限制文件大小为5MB
        files: 10 // 一次最多上传10个文件
    },
    fileFilter: function (req, file, cb) {
        const filetypes = /jpeg|jpg|png|gif/;
        const mimetype = filetypes.test(file.mimetype);
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('只支持图片文件上传！'));
    }
});

// 创建支持PDF和图片文件的上传配置
const uploadFile = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024, // 限制文件大小为10MB（PDF文件可能较大）
        files: 10 // 一次最多上传10个文件
    },
    fileFilter: function (req, file, cb) {
        const imageTypes = /jpeg|jpg|png|gif/;
        const pdfType = /pdf/;
        const mimetype = imageTypes.test(file.mimetype) || pdfType.test(file.mimetype);
        const extname = imageTypes.test(path.extname(file.originalname).toLowerCase()) ||
                       pdfType.test(path.extname(file.originalname).toLowerCase());

        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('只支持图片文件（JPG、PNG、GIF）和PDF文件上传！'));
    }
});

// 数据库连接配置
const db = mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'beiqifxl',
    database: 'company_website'
});



// 禁用严格模式，允许NULL值和空字符串
db.query("SET SESSION sql_mode='NO_ENGINE_SUBSTITUTION'", (err) => {
    if (!err) {
        console.log('成功设置SQL模式为NO_ENGINE_SUBSTITUTION');
    }
});

// 检查并添加html_content字段到wechat_articles表
function checkAndAddHtmlContentColumn() {
    const checkColumnSQL = `
        SELECT COUNT(*) as has_html_content
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'company_website'
        AND TABLE_NAME = 'wechat_articles'
        AND COLUMN_NAME = 'html_content'
    `;

    db.query(checkColumnSQL, (err, results) => {
        if (err) {
            console.error('检查html_content字段失败:', err);
            return;
        }

        const hasHtmlContent = results[0].has_html_content > 0;

        if (!hasHtmlContent) {
            console.log('添加html_content字段到wechat_articles表...');
            const addColumnSQL = `
                ALTER TABLE wechat_articles
                ADD COLUMN html_content LONGTEXT COMMENT '完整的HTML内容，包含样式和格式'
            `;

            db.query(addColumnSQL, (err) => {
                if (err) {
                    console.error('添加html_content字段失败:', err);
                } else {
                    console.log('✅ html_content字段添加成功');
                }
            });
        } else {
            console.log('✅ html_content字段已存在');
        }
    });
}

// 启动时检查字段
checkAndAddHtmlContentColumn();

// 检查并添加html_content字段到support表
function checkAndAddSupportHtmlContentColumn() {
    const checkColumnSQL = `
        SELECT COUNT(*) as has_html_content
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'company_website'
        AND TABLE_NAME = 'support'
        AND COLUMN_NAME = 'html_content'
    `;

    db.query(checkColumnSQL, (err, results) => {
        if (err) {
            console.error('检查support表html_content字段失败:', err);
            return;
        }

        const hasHtmlContent = results[0].has_html_content > 0;

        if (!hasHtmlContent) {
            console.log('添加html_content字段到support表...');
            const addColumnSQL = `
                ALTER TABLE support
                ADD COLUMN html_content LONGTEXT COMMENT '完整的HTML内容，包含样式和格式（参考微信文章）'
                AFTER content
            `;

            db.query(addColumnSQL, (err) => {
                if (err) {
                    console.error('添加support表html_content字段失败:', err);
                } else {
                    console.log('✅ support表html_content字段添加成功');
                }
            });
        } else {
            console.log('✅ support表html_content字段已存在');
        }
    });
}

// 启动时检查support表字段
checkAndAddSupportHtmlContentColumn();

// 处理数据库连接断开的情况
db.on('error', function(err) {
    if(err.code === 'PROTOCOL_CONNECTION_LOST') {
        handleDisconnect();
    } else {
        throw err;
    }
});

function handleDisconnect() {
    db.connect(function(err) {
        if(err) {
            setTimeout(handleDisconnect, 2000);
        }
    });
}

// 初始化数据库表
function initDatabaseTables() {
    console.log('检查并初始化数据库表...');
    
    // 创建passwords表
    const createPasswordsTable = `
        CREATE TABLE IF NOT EXISTS passwords (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(100) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    `;
    
    db.query(createPasswordsTable, (err) => {
        if (!err) {
            // 检查是否有默认用户，如果没有则添加
            db.query('SELECT COUNT(*) as count FROM passwords', (err, results) => {
                if (!err && results[0].count === 0) {
                    // 添加默认用户 (用户名: admin, 密码: 123)
                    db.query('INSERT INTO passwords (username, password) VALUES (?, ?)', ['admin', '123']);
                }
            });
        }
    });
    
    // 创建products表（如果不存在）
        const createProductsTable = `
    CREATE TABLE IF NOT EXISTS products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL COMMENT '产品标题',
            description TEXT COMMENT '产品描述内容',
            info_type VARCHAR(50) NOT NULL COMMENT '产品类型(导航栏、开发板、无线投屏、智能板卡、人工智能等)',
            \`show\` TINYINT(1) DEFAULT 1 COMMENT '是否显示',
            lang TINYINT(1) DEFAULT 0 COMMENT '语言类型(0中文/1英文)',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            update_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            display_order INT DEFAULT 100 COMMENT '显示顺序，数字越小越靠前',
            image VARCHAR(255) DEFAULT NULL COMMENT '产品图片路径',
            url VARCHAR(255) DEFAULT NULL COMMENT '产品链接地址'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `;
        
        db.query(createProductsTable, (err) => {
            if (err) {
                console.error('创建products表失败:', err);
            } else {
            console.log('products表已创建或已存在');
            }
    });
    
    // 创建home_pics表
    const createHomePicsTable = `
        CREATE TABLE IF NOT EXISTS home_pics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            pic_idx INT NOT NULL DEFAULT 1,
            \`show\` BOOLEAN NOT NULL DEFAULT TRUE,
            pic VARCHAR(255) NOT NULL,
            url VARCHAR(255),
            title VARCHAR(255) DEFAULT NULL,
            description TEXT DEFAULT NULL,
            lang INT NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            update_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
            create_time BIGINT NULL
        )
    `;
    
    db.query(createHomePicsTable, (err) => {
        if (!err) {
            // 检查是否存在update_time和create_time字段
            const checkColumnsSQL = `
                SELECT 
                    SUM(CASE WHEN COLUMN_NAME = 'update_time' THEN 1 ELSE 0 END) as has_update_time,
                    SUM(CASE WHEN COLUMN_NAME = 'create_time' THEN 1 ELSE 0 END) as has_create_time,
                    SUM(CASE WHEN COLUMN_NAME = 'title' THEN 1 ELSE 0 END) as has_title,
                    SUM(CASE WHEN COLUMN_NAME = 'description' THEN 1 ELSE 0 END) as has_description
                FROM information_schema.COLUMNS 
                WHERE TABLE_SCHEMA = 'company_website' 
                AND TABLE_NAME = 'home_pics' 
                AND COLUMN_NAME IN ('update_time', 'create_time', 'title', 'description')
            `;
            
            db.query(checkColumnsSQL, (err, results) => {
                if (!err) {
                    const hasUpdateTime = results[0].has_update_time > 0;
                    const hasCreateTime = results[0].has_create_time > 0;
                    const hasTitle = results[0].has_title > 0;
                    const hasDescription = results[0].has_description > 0;
                    
                    // 添加缺少的字段
                    if (!hasUpdateTime) {
                        db.query('ALTER TABLE home_pics ADD COLUMN update_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP');
                    }
                    
                    if (!hasCreateTime) {
                        db.query('ALTER TABLE home_pics ADD COLUMN create_time BIGINT NULL', (err) => {
                            if (!err) {
                                // 为现有记录添加create_time值
                                const now = Date.now();
                                db.query('UPDATE home_pics SET create_time = ? WHERE create_time IS NULL', [now]);
                            }
                        });
                    }
                    
                    // 添加title字段（如果不存在）
                    if (!hasTitle) {
                        db.query('ALTER TABLE home_pics ADD COLUMN title VARCHAR(255) DEFAULT NULL', (err) => {
                            if (err) {
                                console.error('添加title字段失败:', err);
                            } else {
                                console.log('成功添加title字段到home_pics表');
                            }
                        });
                    }
                    
                    // 添加description字段（如果不存在）
                    if (!hasDescription) {
                        db.query('ALTER TABLE home_pics ADD COLUMN description TEXT DEFAULT NULL', (err) => {
                            if (err) {
                                console.error('添加description字段失败:', err);
                            } else {
                                console.log('成功添加description字段到home_pics表');
                            }
                        });
                    }
                }
            });
        }
    });
    
    // 创建产品推荐表
    const createProductRecommendTable = `
        CREATE TABLE IF NOT EXISTS product_recommend (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(100) NOT NULL,
            description TEXT,
            icon VARCHAR(255) NOT NULL,
            url VARCHAR(255),
            sort_order INT NOT NULL DEFAULT 0,
            \`show\` BOOLEAN NOT NULL DEFAULT TRUE,
            lang INT NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            update_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
            create_time BIGINT NULL
        )
    `;
    
    db.query(createProductRecommendTable, (err) => {
        if (!err) {
            // 检查是否存在lang字段
            const checkLangColumnSQL = `
                SELECT COUNT(*) as count 
                FROM information_schema.COLUMNS 
                WHERE TABLE_SCHEMA = 'company_website' 
                AND TABLE_NAME = 'product_recommend' 
                AND COLUMN_NAME = 'lang'`;
                
            db.query(checkLangColumnSQL, (err, results) => {
                if (!err && results[0].count === 0) {
                    // 添加lang字段
                    db.query('ALTER TABLE product_recommend ADD COLUMN lang INT NOT NULL DEFAULT 0');
                }
            });
            
            // 检查是否有默认数据，如果没有则添加
            db.query('SELECT COUNT(*) as count FROM product_recommend', (err, results) => {
                if (!err && results[0].count === 0) {
                    // 添加默认产品推荐数据
                    const defaultProducts = [
                        {
                            title: '无线投屏',
                            description: '贝启无线投屏系统是一套基于无线WiFi技术的软硬件一体的无线投屏解决方案',
                            icon: '/media/1611539616_AirPlay.png',
                            url: 'http://www.bearkey.com.cn/product.html',
                            sort_order: 1
                        },
                        {
                            title: '96BoardsSoM',
                            description: '搭载RK3399Pro的超高性能核心板TB-96AI，搭载RK1808的超低功耗核心板TB-96AIoT',
                            icon: '/media/1611539683_zhuban.png',
                            url: 'http://www.bearkey.com.cn/product.html',
                            sort_order: 2
                        },
                        {
                            title: '人工智能',
                            description: '基于RK3399Pro开发板、RK3399开发板，贝启可以提供一站式人脸识别解决方案',
                            icon: '/media/1611539802_renlianshibie.png',
                            url: 'http://www.bearkey.com.cn/product.html',
                            sort_order: 3
                        },
                        {
                            title: '智能语音',
                            description: '贝启自研智能音频模块，广泛应用于AI语音对话智能儿童故事机、智能家居AI语音控制设备等领域',
                            icon: '/media/1611539852_yinpin.png',
                            url: 'http://www.bearkey.com.cn/product.html',
                            sort_order: 4
                        }
                    ];
                    
                    // 插入默认数据
                    const now = Date.now();
                    defaultProducts.forEach(product => {
                        db.query(
                            'INSERT INTO product_recommend (title, description, icon, url, sort_order, lang, create_time) VALUES (?, ?, ?, ?, ?, ?, ?)',
                            [product.title, product.description, product.icon, product.url, product.sort_order, 0, now]
                        );
                    });
                }
            });
        }
    });
    
    // 创建公司详情表
    const createCompanyInfoTable = `
        CREATE TABLE IF NOT EXISTS company_info (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(500) DEFAULT NULL,
            content TEXT DEFAULT NULL,
            image_path VARCHAR(255) DEFAULT NULL,
            info_type VARCHAR(50) NOT NULL,
            \`show\` BOOLEAN NOT NULL DEFAULT TRUE,
            lang INT NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            update_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
            create_time BIGINT NULL,
            display_order INT DEFAULT 100 COMMENT '显示顺序，数字越小越靠前',
            url VARCHAR(255) DEFAULT NULL COMMENT '链接地址，主要用于产品方案等类型'
        )
    `;
    
    db.query(createCompanyInfoTable, (err) => {
        if (!err) {
            // 检查是否存在update_time、create_time和display_order字段
            const checkColumnsSQL = `
                SELECT 
                    SUM(CASE WHEN COLUMN_NAME = 'update_time' THEN 1 ELSE 0 END) as has_update_time,
                    SUM(CASE WHEN COLUMN_NAME = 'create_time' THEN 1 ELSE 0 END) as has_create_time,
                    SUM(CASE WHEN COLUMN_NAME = 'display_order' THEN 1 ELSE 0 END) as has_display_order,
                    SUM(CASE WHEN COLUMN_NAME = 'image_path' THEN 1 ELSE 0 END) as has_image_path,
                    SUM(CASE WHEN COLUMN_NAME = 'url' THEN 1 ELSE 0 END) as has_url
                FROM information_schema.COLUMNS 
                WHERE TABLE_SCHEMA = 'company_website' 
                AND TABLE_NAME = 'company_info' 
                AND COLUMN_NAME IN ('update_time', 'create_time', 'display_order', 'image_path', 'url')
            `;
            
            db.query(checkColumnsSQL, (err, results) => {
                if (!err) {
                    const hasUpdateTime = results[0].has_update_time > 0;
                    const hasCreateTime = results[0].has_create_time > 0;
                    const hasDisplayOrder = results[0].has_display_order > 0;
                    const hasImagePath = results[0].has_image_path > 0;
                    const hasUrl = results[0].has_url > 0;
                    
                    // 添加缺少的字段
                    if (!hasUpdateTime) {
                        db.query('ALTER TABLE company_info ADD COLUMN update_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP');
                    }
                    
                    if (!hasCreateTime) {
                        db.query('ALTER TABLE company_info ADD COLUMN create_time BIGINT NULL', (err) => {
                            if (!err) {
                                // 为现有记录添加create_time值
                                const now = Date.now();
                                db.query('UPDATE company_info SET create_time = ? WHERE create_time IS NULL', [now]);
                            }
                        });
                    }
                    
                    // 添加display_order字段（如果不存在）
                    if (!hasDisplayOrder) {
                        db.query('ALTER TABLE company_info ADD COLUMN display_order INT DEFAULT 100 COMMENT "显示顺序，数字越小越靠前"', (err) => {
                            if (!err) {
                                // 为现有记录添加默认display_order值
                                db.query('UPDATE company_info SET display_order = 100 WHERE display_order IS NULL');
                            }
                        });
                    }
                    
                    // 添加image_path字段（如果不存在）
                    if (!hasImagePath) {
                        db.query('ALTER TABLE company_info ADD COLUMN image_path VARCHAR(255) DEFAULT NULL', (err) => {
                            if (!err) {
                                // 将现有记录中的图片路径从content字段移到image_path字段
                                db.query(`
                                    UPDATE company_info 
                                    SET image_path = content, 
                                        content = NULL 
                                    WHERE content LIKE '/uploads/%'
                                `);
                            }
                        });
                    }
                    
                    // 添加url字段（如果不存在）
                    if (!hasUrl) {
                        db.query('ALTER TABLE company_info ADD COLUMN url VARCHAR(255) DEFAULT NULL COMMENT "链接地址，主要用于产品方案等类型"');
                    }
                }
            });
        }
    });
    
    // 创建合作伙伴表
    const createPartnerPicsTable = `
        CREATE TABLE IF NOT EXISTS partner_pics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            pic_idx INT NOT NULL DEFAULT 1,
            \`show\` BOOLEAN NOT NULL DEFAULT TRUE,
            pic VARCHAR(255) NOT NULL,
            url VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            update_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
        )
    `;

    db.query(createPartnerPicsTable, (err) => {
        if (!err) {
            console.log('partner_pics表创建成功或已存在');
        }
    });
    
    // 创建产品详情表
    const createProductsInfoTable = `
        CREATE TABLE IF NOT EXISTS products_info (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL COMMENT '产品标题',
            content TEXT COMMENT '产品描述内容',
            info_type VARCHAR(100) COMMENT '产品类型(用户自定义输入)',
            \`show\` TINYINT(1) DEFAULT 1 COMMENT '是否显示',
            lang TINYINT(1) DEFAULT 0 COMMENT '语言类型(0中文/1英文)',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            update_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            display_order INT DEFAULT 100 COMMENT '显示顺序，数字越小越靠前',
            image_path VARCHAR(255) DEFAULT NULL COMMENT '产品图片路径',
            url VARCHAR(255) DEFAULT NULL COMMENT '产品链接地址'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    db.query(createProductsInfoTable, (err) => {
        if (err) {
            console.error('创建products_info表失败:', err);
        } else {
            console.log('products_info表已创建或已存在');
        }
    });

    // ====== 资料下载导航栏表及API ======
    // 创建download_nav表
    const createDownloadNavTable = `
        CREATE TABLE IF NOT EXISTS download_nav (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type VARCHAR(20) NOT NULL DEFAULT 'nav' COMMENT '类型：nav/导航，product/产品',
            title VARCHAR(255) NOT NULL DEFAULT '' COMMENT '标题',
            content TEXT DEFAULT NULL COMMENT '文字内容',
            image_path VARCHAR(255) DEFAULT NULL COMMENT '图片路径',
            display_order INT DEFAULT 100 COMMENT '显示顺序，数字越小越靠前',
            lang TINYINT(1) DEFAULT 0 COMMENT '语言类型(0中文/1英文)',
            \`show\` TINYINT(1) DEFAULT 1 COMMENT '是否显示',
            update_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            url VARCHAR(255) DEFAULT NULL COMMENT '链接url'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    db.query(createDownloadNavTable, (err) => {
        if (err) {
            // 如果字段已存在则忽略
            if (err.message && err.message.indexOf('Duplicate column name') === -1) {
                console.error('创建download_nav表失败:', err);
            }
        } else {
            console.log('download_nav表已创建或已存在');
        }
        // 确保image_path字段存在
        db.query("SHOW COLUMNS FROM download_nav LIKE 'image_path'", (err, results) => {
            if (!err && results.length === 0) {
                db.query("ALTER TABLE download_nav ADD COLUMN image_path VARCHAR(255) DEFAULT NULL COMMENT '图片路径'", () => {});
            }
        });
    });
    }

    // ====== 资料下载详情表及API ======
    // 初始化download_detail表
    const createDownloadDetailTable = `
        CREATE TABLE IF NOT EXISTS download_detail (
          id INT AUTO_INCREMENT PRIMARY KEY,
          type VARCHAR(64) DEFAULT NULL COMMENT '分类类型（可自定义输入）',
          title VARCHAR(255) DEFAULT NULL COMMENT '资料标题',
          content TEXT COMMENT '资料描述',
          display_order INT DEFAULT 100 COMMENT '显示顺序，数字越小越靠前',
          lang TINYINT DEFAULT 0 COMMENT '语言：0中文 1英文',
          \`show\` TINYINT DEFAULT 1 COMMENT '是否显示：1显示 0不显示',
          update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          url VARCHAR(255) DEFAULT NULL COMMENT '下载链接',
          image_path VARCHAR(255) DEFAULT NULL COMMENT '资料图片（可选）'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资料下载详情';
    `;
    db.query(createDownloadDetailTable, (err) => {
        if (err) {
            console.error('创建download_detail表失败:', err);
        } else {
            console.log('download_detail表已创建或已存在');
        }
    });

    // 创建服务与支持表
    const createSupportTable = `
        CREATE TABLE IF NOT EXISTS support (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type VARCHAR(64) DEFAULT NULL COMMENT '支持类型（菜单、详情）',
            title VARCHAR(255) DEFAULT NULL COMMENT '支持标题',
            content TEXT COMMENT '支持内容描述',
            html_content LONGTEXT COMMENT '完整的HTML内容，包含样式和格式（参考微信文章）',
            display_order INT DEFAULT 100 COMMENT '显示顺序，数字越小越靠前',
            lang TINYINT DEFAULT 0 COMMENT '语言：0中文 1英文',
            \`show\` TINYINT DEFAULT 1 COMMENT '是否显示：1显示 0不显示',
            update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            url VARCHAR(255) DEFAULT NULL COMMENT '链接地址',
            image_path VARCHAR(255) DEFAULT NULL COMMENT '图片或PDF文件路径',
            source_id INT DEFAULT NULL COMMENT '关联的中文记录ID（用于英文翻译记录）'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务与支持';
    `;
    db.query(createSupportTable, (err) => {
        if (err) {
            console.error('创建support表失败:', err);
        } else {
            console.log('support表已创建或已存在');

            // 确保source_id字段存在（参考download_nav表的方式）
            db.query("SHOW COLUMNS FROM support LIKE 'source_id'", (err, results) => {
                if (!err && results.length === 0) {
                    db.query("ALTER TABLE support ADD COLUMN source_id INT DEFAULT NULL COMMENT '关联的中文记录ID（用于英文翻译记录）'", (err) => {
                        if (err) {
                            console.error('添加source_id字段失败:', err);
                        } else {
                            console.log('✅ source_id字段已成功添加');
                        }
                    });
                } else if (!err) {
                    console.log('✅ source_id字段已存在');
                }
            });
        }
    });

    // 创建商业定制表
    const createBusinessCustomizationTable = `
        CREATE TABLE IF NOT EXISTS business_customization (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            email VARCHAR(100) NOT NULL,
            company VARCHAR(100) NOT NULL,
            custom_type VARCHAR(50) NOT NULL,
            requirements TEXT NOT NULL,
            submit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            update_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;

    // 在initDatabaseTables函数中添加创建business_customization表的代码
    db.query(createBusinessCustomizationTable, (err) => {
        if (err) {
            console.error('创建business_customization表失败:', err);
        } else {
            console.log('business_customization表已创建或已存在');
        }
    });




// 连接数据库
db.connect((err) => {
    if (err) {
        console.error('数据库连接失败，详细错误:', err);
        console.error('错误堆栈:', err.stack);
        setTimeout(handleDisconnect, 2000);
        return;
    }
    console.log('数据库连接成功');
    // 确保company_info表的title和content字段允许为NULL
    const alterColumnsSQL = `
        ALTER TABLE company_info 
        MODIFY COLUMN title VARCHAR(500) DEFAULT NULL,
        MODIFY COLUMN content TEXT DEFAULT NULL;
    `;
    
    db.query(alterColumnsSQL, (err) => {
        if (!err) {
            console.log('成功确保title和content字段允许为NULL');
        }
    });

    // 初始化数据库表
    initDatabaseTables();

    // 检查sort_order字段是否存在
    const checkColumnSQL = `
        SELECT COUNT(*) as count 
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = 'company_website' 
        AND TABLE_NAME = 'products' 
        AND COLUMN_NAME = 'sort_order'`;

    db.query(checkColumnSQL, (err, results) => {
        if (!err && results[0].count === 0) {
            // 添加sort_order字段
            const addColumnSQL = `
                ALTER TABLE products 
                ADD COLUMN sort_order INT DEFAULT 0,
                ADD INDEX idx_sort_order (sort_order)`;

            db.query(addColumnSQL, (err) => {
                if (!err) {
                    // 更新现有记录的sort_order
                    const updateOrderSQL = `
                        UPDATE products 
                        SET sort_order = (
                            SELECT t.new_order 
                            FROM (
                                SELECT id, (@row_number:=@row_number + 1) AS new_order
                                FROM products, (SELECT @row_number:=0) AS t
                                ORDER BY created_at DESC
                            ) AS t
                            WHERE t.id = products.id
                        )`;

                    db.query(updateOrderSQL);
                }
            });
        }
    });
});

// API路由
// 获取产品列表
app.get('/apis/product_list/', (req, res) => {
    try {
        // 获取分页参数
        const page = parseInt(req.query.page) || 1;
        const size = parseInt(req.query.size) || 10;
        const offset = (page - 1) * size;
        
        // 构建查询条件
        let whereClause = '1=1';
        let params = [];
        
        // 处理filters参数
        if (req.query.filters) {
            try {
                const filters = JSON.parse(req.query.filters);
                
                if (filters.lang !== undefined && filters.lang !== '') {
                    whereClause += ' AND lang = ?';
                    params.push(filters.lang);
                }
                
                if (filters.info_type !== undefined && filters.info_type !== '') {
                    whereClause += ' AND info_type = ?';
                    params.push(filters.info_type);
                }
            } catch (e) {
                console.error('解析filters参数失败:', e);
            }
        } else {
            // 兼容旧的参数方式
            if (req.query.lang !== undefined && req.query.lang !== '') {
                whereClause += ' AND lang = ?';
                params.push(req.query.lang);
            }
            
            if (req.query.info_type !== undefined && req.query.info_type !== '') {
                whereClause += ' AND info_type = ?';
                params.push(req.query.info_type);
            }
        }
        
        // 检查请求来源
        const isAdminRequest = 
            req.headers['x-client-type'] === 'admin' || 
            req.headers['x-admin-request'] === 'true' ||
            (req.headers['user-agent'] && req.headers['user-agent'].includes('admin')) ||
            req.headers['referer'] && (req.headers['referer'].includes('/admin/') || req.headers['referer'].includes('/admin1/'));
        
        // 前端请求时默认只返回显示状态为true的产品
        if (!isAdminRequest) {
            whereClause += ' AND `show` = 1';
        }
        
        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM products WHERE ${whereClause}`;
        
        db.query(countSql, params, (err, countResult) => {
            if (err) {
                console.error('查询产品列表总数失败:', err);
                return res.json({
                    status: 'error',
                    msg: '查询产品列表总数失败'
                });
            }
            
            const total = countResult[0].total;
            
            // 查询分页数据
            const listSql = `
                SELECT * FROM products 
                WHERE ${whereClause}
                ORDER BY lang ASC, display_order ASC, id DESC
                LIMIT ? OFFSET ?
            `;
            
            db.query(listSql, [...params, size, offset], (err, results) => {
                if (err) {
                    console.error('查询产品列表失败:', err);
                    return res.json({
                        status: 'error',
                        msg: '查询产品列表失败'
                    });
                }
                
                // 处理图片路径
                results.forEach(item => {
                    if (item.image) {
                        // 处理绝对路径问题
                        if (item.image.includes('C:')) {
                            // 如果是Windows绝对路径，提取文件名
                            const fileName = item.image.split('\\').pop().split('/').pop();
                            item.image = '/uploads/' + fileName;
                        }
                        // 确保图片路径正确
                        else if (!item.image.startsWith('http') && !item.image.startsWith('/')) {
                            item.image = '/' + item.image;
                        }
                    }
                    
                    // 为了兼容前端，添加title和content字段
                    item.title = item.name;
                    item.content = item.description;
                    item.image_path = item.image;
                });
                
                res.json({
                    status: 'ok',
                    total: total,
                    data: results
                });
            });
        });
    } catch (error) {
        console.error('获取产品列表错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 获取产品详情
app.get('/apis/product_detail/', (req, res) => {
    try {
        const id = req.query.id;
        
        if (!id) {
            return res.json({
                status: 'error',
                msg: '缺少ID参数'
            });
        }
        
        const sql = 'SELECT * FROM products WHERE id = ?';
        
        db.query(sql, [id], (err, results) => {
            if (err) {
                console.error('查询产品详情失败:', err);
                return res.json({
                    status: 'error',
                    msg: '查询产品详情失败'
                });
            }
            
            if (results.length === 0) {
                return res.json({
                    status: 'error',
                    msg: '未找到对应的产品'
                });
            }
            
            // 处理图片路径
            if (results[0].image) {
                if (results[0].image.includes('C:')) {
                    const fileName = results[0].image.split('\\').pop().split('/').pop();
                    results[0].image = '/uploads/' + fileName;
                }
                else if (!results[0].image.startsWith('http') && !results[0].image.startsWith('/')) {
                    results[0].image = '/' + results[0].image;
                }
            }
            
            // 为了兼容前端，添加title和content字段
            results[0].title = results[0].name;
            results[0].content = results[0].description;
            results[0].image_path = results[0].image;
            
            res.json({
                status: 'ok',
                data: results[0]
            });
        });
    } catch (error) {
        console.error('获取产品详情错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 添加新产品
app.post('/api/products', upload.single('image'), (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: '请上传图片文件' });
        }

        // 获取当前最大的sort_order
        db.query('SELECT COALESCE(MAX(sort_order), 0) as maxOrder FROM products', (err, results) => {
            if (err) {
                console.error('获取最大排序值失败:', err.message);
                res.status(500).json({ error: err.message });
                return;
            }

            const nextOrder = (results[0].maxOrder || 0) + 1;
            const { name, description } = req.body;
            const image = `/uploads/${req.file.filename}`;
            
            const sql = 'INSERT INTO products (name, description, image, sort_order) VALUES (?, ?, ?, ?)';
            db.query(sql, [
                name || '轮播图',
                description || '轮播图描述',
                image,
                nextOrder
            ], (err, result) => {
                if (err) {
                    console.error('数据库插入错误:', err.message);
                    res.status(500).json({ error: err.message });
                    return;
                }
                res.json({ 
                    id: result.insertId,
                    message: '轮播图添加成功',
                    image: image
                });
            });
        });
    } catch (error) {
        console.error('服务器错误:', error.message);
        res.status(500).json({ error: error.message });
    }
});

// 更新产品排序
app.put('/api/products/:id/sort', (req, res) => {
    try {
        const { sort_order } = req.body;
        if (typeof sort_order !== 'number') {
            return res.status(400).json({ error: '排序值必须是数字' });
        }

        const sql = 'UPDATE products SET sort_order = ? WHERE id = ?';
        db.query(sql, [sort_order, req.params.id], (err, result) => {
            if (err) {
                console.error('更新排序失败:', err.message);
                res.status(500).json({ error: err.message });
                return;
            }
            if (result.affectedRows === 0) {
                res.status(404).json({ error: '产品不存在' });
                return;
            }
            res.json({ message: '排序更新成功' });
        });
    } catch (error) {
        console.error('服务器错误:', error.message);
        res.status(500).json({ error: error.message });
    }
});

// 删除产品
app.delete('/api/products/:id', (req, res) => {
    try {
        // 先获取图片路径
        db.query('SELECT image FROM products WHERE id = ?', [req.params.id], (err, results) => {
            if (err || !results.length) {
                res.status(500).json({ error: err?.message || '产品不存在' });
                return;
            }

            const imagePath = results[0].image;
            
            // 删除数据库记录
            db.query('DELETE FROM products WHERE id = ?', [req.params.id], (err, result) => {
                if (err) {
                    res.status(500).json({ error: err.message });
                    return;
                }

                // 删除图片文件
                if (imagePath) {
                    const fullPath = path.join(__dirname, 'public', imagePath);
                    if (fs.existsSync(fullPath)) {
                        fs.unlink(fullPath, (err) => {
                            if (err) {
                                console.error('删除图片文件失败:', err);
                            }
                        });
                    }
                }

                res.json({ message: '产品删除成功' });
            });
        });
    } catch (error) {
        console.error('服务器错误:', error);
        res.status(500).json({ error: error.message });
    }
});

// 登录API接口
app.post('/apis/login/', (req, res) => {
    try {
        const { username, password } = req.body;
        
        if (!username || !password) {
            return res.json({ status: 'error', msg: '用户名和密码不能为空' });
        }
        
        // 查询数据库验证用户名和密码
        const sql = 'SELECT * FROM passwords WHERE username = ? AND password = ?';
        db.query(sql, [username, password], (err, results) => {
            if (err) {
                return res.json({ status: 'error', msg: '服务器内部错误' });
            }
            
            if (results.length > 0) {
                // 登录成功
                return res.json({ status: 'ok', msg: '登录成功' });
            } else {
                // 登录失败
                return res.json({ status: 'error', msg: '用户名或密码错误' });
            }
        });
    } catch (error) {
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 登出API接口
app.post('/apis/logout/', (req, res) => {
    try {
        // 简单返回成功状态，实际的登出逻辑在前端处理（清除sessionStorage）
        return res.json({ status: 'ok', msg: '登出成功' });
    } catch (error) {
        console.error('登出处理错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 翻译API接口 - 解决CORS问题
app.post('/api/translate', async (req, res) => {
    try {
        const { text, from = 'zh', to = 'en' } = req.body;

        if (!text || !text.trim()) {
            return res.json({
                success: false,
                error: '翻译文本不能为空'
            });
        }

        console.log(`需要翻译文本:`, text);

        // 按优先级尝试所有翻译服务（Google翻译优先，然后百度翻译，最后其他备用）
        const translationMethods = [
            () => translateWithLingva(text, from, to),
            () => translateWithBaidu(text, from, to),
            () => translateWithMyMemoryAPI(text, from, to),
            () => translateWithLibreTranslate(text, from, to),
            () => translateWithSimpleTranslate(text, from, to),
        ];

        // 定义翻译服务名称映射
        const serviceNames = ['Google翻译(Lingva)', '百度翻译', 'MyMemory', 'LibreTranslate', 'SimpleTranslate'];

        // 逐一尝试翻译方法
        for (let i = 0; i < translationMethods.length; i++) {
            const serviceName = serviceNames[i] || `翻译服务${i + 1}`;
            try {
                console.log(`正在尝试${serviceName}...`);
                const result = await translationMethods[i]();

                if (result && result.success && result.translatedText && result.translatedText !== text) {
                    console.log(`✅ ${serviceName}翻译成功:`, text.substring(0, 50) + '...', '->', result.translatedText.substring(0, 50) + '...');
                    return res.json({
                        success: true,
                        translatedText: result.translatedText,
                        originalText: text,
                        service: result.service,
                        usedService: serviceName
                    });
                } else {
                    console.warn(`❌ ${serviceName}返回无效结果`);
                }
            } catch (error) {
                console.warn(`❌ ${serviceName}失败:`, error.message);
                continue;
            }
        }

        // 所有翻译方法都失败，返回详细错误信息
        console.error('🚫 所有翻译服务都不可用，返回原文');
        console.log('尝试的服务列表:', serviceNames.join(', '));

        return res.json({
            success: false,
            error: `所有翻译服务都不可用，已尝试: ${serviceNames.join(', ')}`,
            translatedText: text,
            originalText: text,
            service: 'none',
            attemptedServices: serviceNames
        });

    } catch (error) {
        console.error('翻译API错误:', error);
        res.json({
            success: false,
            error: '服务器内部错误'
        });
    }
});

// 获取首页图片列表API
app.post('/apis/get_home_pic/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { page = 1, page_size = 10, order_by, filters } = req.body;
        
        let filtersObj = {};
        if (filters) {
            try {
                filtersObj = JSON.parse(filters);
            } catch (e) {
                // 解析失败，使用空对象
            }
        }
        
        // 构建WHERE子句
        let whereClause = '1=1';
        const queryParams = [];
        
        if (filtersObj.id) {
            whereClause += ' AND id = ?';
            queryParams.push(filtersObj.id);
        }
        
        if (filtersObj.lang !== undefined && filtersObj.lang !== "") {
            whereClause += ' AND lang = ?';
            queryParams.push(Number(filtersObj.lang));
        }
        
        // 检查请求来源
        const isAdminRequest = 
            req.headers['x-client-type'] === 'admin' || 
            req.headers['x-admin-request'] === 'true' ||
            (req.headers['user-agent'] && req.headers['user-agent'].includes('admin')) ||
            req.headers['referer'] && (req.headers['referer'].includes('/admin/') || req.headers['referer'].includes('/admin1/'));
        
        // 前端请求时默认只返回显示状态为true的轮播图
        // 管理后台请求时返回所有记录
        if (!isAdminRequest) {
            // 前端请求，只返回显示状态为true的记录
            whereClause += ' AND `show` = 1';
        }
        
        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM home_pics WHERE ${whereClause}`;
        db.query(countSql, queryParams, (err, countResult) => {
            if (err) {
                return res.json({ status: 'error', msg: '查询数据失败' });
            }
            
            const total = countResult[0].total;
            const totalPages = Math.ceil(total / page_size);
            
            // 查询数据
            const offset = (page - 1) * page_size;
            // 修改排序方式：按pic_idx升序排序（值小的排在前面）
            const dataSql = `SELECT * FROM home_pics WHERE ${whereClause} ORDER BY lang ASC, pic_idx ASC, id DESC LIMIT ? OFFSET ?`;
            
            db.query(dataSql, [...queryParams, parseInt(page_size), offset], (err, results) => {
                if (err) {
                    return res.json({ status: 'error', msg: '查询数据失败' });
                }
                
                // 处理结果数据
                results.forEach((item, index) => {
                    // 确保pic_idx是整数
                    if (item.pic_idx !== null && item.pic_idx !== undefined) {
                        item.pic_idx = parseInt(item.pic_idx, 10);
                    } else {
                        item.pic_idx = index; // 如果没有pic_idx，使用索引作为默认值
                    }
                    
                    // 确保show字段是数字1或0
                    item.show = item.show ? 1 : 0;
                });
                
                return res.json({
                    status: 'ok',
                    data_list: results,
                    page: parseInt(page),
                    page_size: parseInt(page_size),
                    total_data: total,
                    total_pages: totalPages
                });
            });
        });
    } catch (error) {
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 创建首页图片API
app.post('/apis/create_home_pic/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { pic_idx, show, pic, url, lang, create_time, title, description } = req.body;
        
        // 确保pic_idx是整数
        const picIdx = parseInt(pic_idx, 10);
        if (isNaN(picIdx)) {
            return res.json({ status: 'error', msg: '显示顺序必须为整数' });
        }
        
        // 处理show字段，确保是数字1或0
        let showValue = 0; // 默认不显示
        if (show === 'True' || show === 'true' || show === '1' || show === 1 || show === true) {
            showValue = 1;
        }
        
        // 处理create_time字段
        const createTimeValue = create_time || Date.now();
        
        const sql = 'INSERT INTO home_pics (pic_idx, `show`, pic, url, lang, create_time, title, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?)';
        db.query(sql, [picIdx, showValue, pic, url, lang, createTimeValue, title, description], (err, result) => {
            if (err) {
                return res.json({ status: 'error', msg: '创建数据失败' });
            }
            
            // 通知所有客户端刷新轮播图
            notifyCarouselRefresh();
            
            return res.json({
                status: 'ok',
                msg: '创建成功',
                id: result.insertId
            });
        });
    } catch (error) {
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 更新首页图片API
app.post('/apis/update_home_pic/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { pic_idx, pic, url, filters, show, lang, update_time, title, description } = req.body;
        
        console.log('收到更新首页图片请求:', {
            pic_idx,
            pic,
            url,
            filters,
            show,
            lang,
            update_time,
            title,
            description
        });
        
        // 确保pic_idx是整数
        const picIdx = parseInt(pic_idx, 10);
        if (isNaN(picIdx)) {
            return res.json({ status: 'error', msg: '显示顺序必须为整数' });
        }
        
        // 处理show字段，确保是数字1或0
        let showValue = 0; // 默认不显示
        if (show === 'True' || show === 'true' || show === '1' || show === 1 || show === true) {
            showValue = 1;
        }
        
        // 处理update_time字段
        const updateTimeValue = update_time || Date.now();
        
        console.log(`处理后的值: pic_idx=${picIdx}, show=${showValue}, update_time=${updateTimeValue}`);
        
        let filtersObj = {};
        if (filters) {
            try {
                filtersObj = JSON.parse(filters);
                console.log('解析后的filters:', filtersObj);
            } catch (e) {
                console.error('解析filters参数失败:', e);
                return res.json({ status: 'error', msg: '参数格式错误' });
            }
        }
        
        if (!filtersObj.id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        
        const sql = 'UPDATE home_pics SET pic_idx = ?, pic = ?, url = ?, `show` = ?, lang = ?, update_time = FROM_UNIXTIME(? / 1000), title = ?, description = ? WHERE id = ?';
        db.query(sql, [picIdx, pic, url, showValue, lang, updateTimeValue, title, description, filtersObj.id], (err, result) => {
            if (err) {
                console.error('更新首页图片错误:', err);
                return res.json({ status: 'error', msg: '更新数据失败' });
            }
            
            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到要更新的数据' });
            }
            
            // 记录操作日志
            console.log(`成功更新ID=${filtersObj.id}的轮播图，显示状态=${showValue}，排序=${picIdx}`);
            
            // 通知所有客户端刷新轮播图
            notifyCarouselRefresh();
            
            return res.json({
                status: 'ok',
                msg: '更新成功'
            });
        });
    } catch (error) {
        console.error('更新首页图片错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 删除首页图片API
app.post('/apis/delete_home_pic/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { filters } = req.body;
        
        let filtersObj = {};
        if (filters) {
            try {
                filtersObj = JSON.parse(filters);
            } catch (e) {
                console.error('解析filters参数失败:', e);
                return res.json({ status: 'error', msg: '参数格式错误' });
            }
        }
        
        if (!filtersObj.id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        
        // 先查询图片路径
        db.query('SELECT pic FROM home_pics WHERE id = ?', [filtersObj.id], (err, results) => {
            if (err) {
                console.error('查询图片路径错误:', err);
                return res.json({ status: 'error', msg: '查询数据失败' });
            }
            
            if (results.length === 0) {
                return res.json({ status: 'error', msg: '未找到要删除的数据' });
            }
            
            const picPath = results[0].pic;
            
            // 删除数据库记录
            db.query('DELETE FROM home_pics WHERE id = ?', [filtersObj.id], (err, result) => {
                if (err) {
                    console.error('删除首页图片错误:', err);
                    return res.json({ status: 'error', msg: '删除数据失败' });
                }
                
                // 尝试删除图片文件（如果是本地存储的）
                if (picPath && picPath.startsWith('/uploads/')) {
                    const fullPath = path.join(__dirname, 'public', picPath);
                    
                    // 在删除前记录删除操作
                    try {
                        const filename = path.basename(fullPath);
                        logMediaOperation('删除', filename, fullPath);
                        
                        // 注意：我们不再备份被删除的图片，因为上传时已经备份到media目录
                    } catch (statErr) {
                        console.error('记录删除操作失败:', statErr);
                    }
                    
                    if (fs.existsSync(fullPath)) {
                        // 删除原始文件
                        fs.unlink(fullPath, (err) => {
                            if (err) {
                                console.error('删除图片文件失败:', err);
                            }
                        });
                    }
                }
                
                // 通知所有客户端刷新轮播图
                notifyCarouselRefresh();
                
                return res.json({
                    status: 'ok',
                    msg: '删除成功'
                });
            });
        });
    } catch (error) {
        console.error('删除首页图片错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 添加首页图片上传处理
app.post('/apis/upload_pic/', upload.single('file'), (req, res) => {
    try {
        if (!req.file) {
            console.error('未上传文件');
            return res.json({ status: 'error', msg: '未上传文件' });
        }
        
        const filePath = `/uploads/${req.file.filename}`;
        const fullFilePath = path.join(__dirname, 'public', filePath);
        
        console.log('图片上传成功:', {
            原始文件名: req.file.originalname,
            保存路径: filePath,
            文件大小: req.file.size
        });
        
        // 记录上传日志
        logMediaOperation('上传', req.file.originalname, fullFilePath);
        
        // 备份上传的图片（直接复制到media目录）
        const backupPath = backupMedia(fullFilePath, req.file.originalname);
        
        return res.json({
            status: 'ok',
            msg: '上传成功',
            path: filePath,
            backup_path: backupPath ? path.relative(__dirname, backupPath) : null
        });
    } catch (error) {
        console.error('上传图片错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 添加文件上传处理（支持图片和PDF）
app.post('/apis/upload_file/', uploadFile.single('file'), (req, res) => {
    try {
        if (!req.file) {
            console.error('未上传文件');
            return res.json({ status: 'error', msg: '未上传文件' });
        }

        const filePath = `/uploads/${req.file.filename}`;
        const fullFilePath = path.join(__dirname, 'public', filePath);

        console.log('文件上传成功:', {
            原始文件名: req.file.originalname,
            保存路径: filePath,
            文件大小: req.file.size,
            文件类型: req.file.mimetype
        });

        // 记录上传日志
        logMediaOperation('上传', req.file.originalname, fullFilePath);

        // 备份上传的文件（直接复制到media目录）
        const backupPath = backupMedia(fullFilePath, req.file.originalname);

        return res.json({
            status: 'ok',
            msg: '上传成功',
            path: filePath,
            backup_path: backupPath ? path.relative(__dirname, backupPath) : null
        });
    } catch (error) {
        console.error('文件上传错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 生成首页HTML的API
app.post('/apis/make_index_html/', (req, res) => {
    try {
        // 从home_pics表获取中文网站的轮播图数据
        const sql = 'SELECT * FROM home_pics WHERE lang = 0 AND `show` = true ORDER BY pic_idx DESC';
        db.query(sql, (err, results) => {
            if (err) {
                console.error('查询轮播图数据错误:', err);
                return res.json({ status: 'error', msg: '查询数据失败' });
            }
            
            console.log(`成功获取${results.length}个中文轮播图数据`);
            
            // 这里不实际生成HTML，只返回成功状态
            // 因为前端会直接从home_pics表获取数据
            return res.json({
                status: 'ok',
                msg: '首页生成成功',
                data_count: results.length
            });
        });
    } catch (error) {
        console.error('生成首页HTML错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 生成英文首页HTML的API
app.post('/apis/make_index_html_en/', (req, res) => {
    try {
        // 从home_pics表获取英文网站的轮播图数据
        const sql = 'SELECT * FROM home_pics WHERE lang = 1 AND `show` = true ORDER BY pic_idx DESC';
        db.query(sql, (err, results) => {
            if (err) {
                console.error('查询轮播图数据错误:', err);
                return res.json({ status: 'error', msg: '查询数据失败' });
            }
            
            console.log(`成功获取${results.length}个英文轮播图数据`);
            
            // 这里不实际生成HTML，只返回成功状态
            // 因为前端会直接从home_pics表获取数据
            return res.json({
                status: 'ok',
                msg: '英文首页生成成功',
                data_count: results.length
            });
        });
    } catch (error) {
        console.error('生成英文首页HTML错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 添加更新轮播图显示状态的API
app.post('/apis/update_show_status/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { filters, show, lang, update_time } = req.body;
        
        console.log('收到更新轮播图显示状态请求:', {
            filters,
            show,
            lang,
            update_time
        });
        
        // 处理show字段，确保是数字1或0
        let showValue = 1; // 默认显示
        if (show === 'False' || show === 'false' || show === '0' || show === 0 || show === false) {
            showValue = 0;
        }
        
        // 处理update_time字段
        const updateTimeValue = update_time || Date.now();
        
        let filtersObj = {};
        if (filters) {
            try {
                filtersObj = JSON.parse(filters);
                console.log('解析后的filters:', filtersObj);
            } catch (e) {
                console.error('解析filters参数失败:', e);
                return res.json({ status: 'error', msg: '参数格式错误' });
            }
        }
        
        if (!filtersObj.id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        
        let sql = 'UPDATE home_pics SET `show` = ?, update_time = FROM_UNIXTIME(? / 1000)';
        let params = [showValue, updateTimeValue];
        
        // 如果提供了lang参数，也更新lang字段
        if (lang !== undefined) {
            // 确保lang是数字类型
            const langValue = Number(lang);
            sql += ', `lang` = ?';
            params.push(langValue);
            console.log(`将同时更新ID=${filtersObj.id}的语言类型为${langValue}`);
        }
        
        // 添加WHERE条件
        sql += ' WHERE id = ?';
        params.push(filtersObj.id);
        
        db.query(sql, params, (err, result) => {
            if (err) {
                console.error('更新显示状态错误:', err);
                return res.json({ status: 'error', msg: '更新数据失败' });
            }
            
            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到要更新的数据' });
            }
            
            // 记录操作日志
            console.log(`成功更新ID=${filtersObj.id}的轮播图显示状态为${showValue}${lang !== undefined ? '，语言类型为'+lang : ''}`);
            
            return res.json({
                status: 'ok',
                msg: '更新成功'
            });
        });
    } catch (error) {
        console.error('更新显示状态错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 添加API路由，用于获取媒体备份列表
app.get('/apis/media_backups/', (req, res) => {
    try {
        // 获取查询参数
        const { date, type } = req.query;
        
        console.log('收到获取媒体备份请求:', {
            date,
            type
        });
        
        // 如果指定了日期，查找该日期的目录
        let targetDir = mediaBackupDir;
        if (date) {
            targetDir = path.join(mediaBackupDir, date);
            if (!fs.existsSync(targetDir)) {
                return res.json({
                    status: 'error',
                    msg: `未找到日期 ${date} 的备份记录`
                });
            }
        }
        
        // 读取目录内容
        const dirContents = fs.readdirSync(targetDir, { withFileTypes: true });
        
        // 处理结果
        const result = {
            status: 'ok',
            directories: [],
            files: []
        };
        
        // 处理目录和文件
        dirContents.forEach(item => {
            if (item.isDirectory()) {
                result.directories.push(item.name);
            } else if (item.isFile()) {
                // 如果是日志文件，跳过
                if (item.name === 'upload_log.txt') {
                    return;
                }
                
                // 如果指定了类型过滤，检查文件名
                if (type) {
                    if (type === 'deleted' && !item.name.includes('deleted_')) {
                        return;
                    } else if (type === 'normal' && item.name.includes('deleted_')) {
                        return;
                    }
                }
                
                // 获取文件信息
                const filePath = path.join(targetDir, item.name);
                const stats = fs.statSync(filePath);
                
                result.files.push({
                    name: item.name,
                    path: path.relative(__dirname, filePath).replace(/\\/g, '/'),
                    size: stats.size,
                    created: stats.birthtime,
                    modified: stats.mtime,
                    isDeleted: item.name.includes('deleted_')
                });
            }
        });
        
        // 如果在根目录，添加日志文件信息
        if (!date && fs.existsSync(mediaLogFile)) {
            const logStats = fs.statSync(mediaLogFile);
            result.logFile = {
                name: 'upload_log.txt',
                path: path.relative(__dirname, mediaLogFile).replace(/\\/g, '/'),
                size: logStats.size,
                modified: logStats.mtime
            };
        }
        
        // 返回结果
        return res.json(result);
    } catch (error) {
        console.error('获取媒体备份列表错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 添加API路由，用于获取上传日志内容
app.get('/apis/media_log/', (req, res) => {
    try {
        if (!fs.existsSync(mediaLogFile)) {
            return res.json({
                status: 'error',
                msg: '日志文件不存在'
            });
        }
        
        // 读取日志文件
        const logContent = fs.readFileSync(mediaLogFile, 'utf8');
        
        // 按行分割并解析
        const logLines = logContent.split('\n').filter(line => line.trim());
        
        // 跳过前两行（标题和格式说明）
        const logEntries = logLines.slice(2).map(line => {
            try {
                // 解析日志行
                const timeMatch = line.match(/\[(.*?)\]/);
                const operationMatch = line.match(/\]\s+\[(.*?)\]/);
                const filenameMatch = line.match(/\]\s+\[(.*?)\]\s+\[/);
                const pathMatch = line.match(/\]\s+\[(.*?)\]\s+\[\d+/);
                const sizeMatch = line.match(/\[(\d+)\s+bytes\]/);
                
                return {
                    time: timeMatch ? timeMatch[1] : '',
                    operation: operationMatch ? operationMatch[1] : '',
                    filename: filenameMatch ? filenameMatch[1] : '',
                    path: pathMatch ? pathMatch[1] : '',
                    size: sizeMatch ? parseInt(sizeMatch[1], 10) : 0
                };
            } catch (e) {
                console.error('解析日志行失败:', e, line);
                return null;
            }
        }).filter(entry => entry !== null);
        
        return res.json({
            status: 'ok',
            entries: logEntries
        });
    } catch (error) {
        console.error('获取媒体日志错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 添加API路由，用于获取特定备份文件
app.get('/apis/media_file/:date/:filename', (req, res) => {
    try {
        const { date, filename } = req.params;
        
        // 构建文件路径
        const filePath = path.join(mediaBackupDir, date, filename);
        
        // 检查文件是否存在
        if (!fs.existsSync(filePath)) {
            return res.status(404).json({
                status: 'error',
                msg: '文件不存在'
            });
        }
        
        // 发送文件
        res.sendFile(filePath);
    } catch (error) {
        console.error('获取媒体文件错误:', error);
        res.status(500).json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 产品推荐相关API
// 获取产品推荐列表
app.post('/apis/get_product_recommend/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { page = 1, page_size = 10, order_by, filters } = req.body;
        
        let filtersObj = {};
        if (filters) {
            try {
                filtersObj = JSON.parse(filters);
            } catch (e) {
                // 解析失败，使用空对象
            }
        }
        
        // 构建WHERE子句
        let whereClause = '1=1';
        const queryParams = [];
        
        if (filtersObj.id) {
            whereClause += ' AND id = ?';
            queryParams.push(filtersObj.id);
        }
        
        if (filtersObj.lang !== undefined && filtersObj.lang !== "") {
            whereClause += ' AND lang = ?';
            queryParams.push(Number(filtersObj.lang));
        }
        
        // 检查请求来源
        const isAdminRequest = 
            req.headers['x-client-type'] === 'admin' || 
            req.headers['x-admin-request'] === 'true' ||
            (req.headers['user-agent'] && req.headers['user-agent'].includes('admin')) ||
            req.headers['referer'] && (req.headers['referer'].includes('/admin/') || req.headers['referer'].includes('/admin1/'));
        
        // 前端请求时默认只返回显示状态为true的产品
        // 管理后台请求时返回所有记录
        if (!isAdminRequest) {
            whereClause += ' AND `show` = 1';
        }
        
        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM product_recommend WHERE ${whereClause}`;
        db.query(countSql, queryParams, (err, countResult) => {
            if (err) {
                return res.json({ status: 'error', msg: '查询数据失败' });
            }
            
            const total = countResult[0].total;
            const totalPages = Math.ceil(total / page_size);
            
            // 查询数据
            const offset = (page - 1) * page_size;
            const dataSql = `SELECT * FROM product_recommend WHERE ${whereClause} ORDER BY lang ASC, sort_order ASC, id ASC LIMIT ? OFFSET ?`;
            
            db.query(dataSql, [...queryParams, parseInt(page_size), offset], (err, results) => {
                if (err) {
                    return res.json({ status: 'error', msg: '查询数据失败' });
                }
                
                return res.json({
                    status: 'ok',
                    data_list: results,
                    page: parseInt(page),
                    page_size: parseInt(page_size),
                    total_data: total,
                    total_pages: totalPages
                });
            });
        });
    } catch (error) {
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 创建产品推荐
app.post('/apis/create_product_recommend/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { title, description, icon, url, sort_order, show, lang } = req.body;
        
        console.log('收到创建产品推荐请求:', {
            title,
            description,
            icon,
            url,
            sort_order,
            show,
            lang
        });
        
        // 确保sort_order是整数
        const sortOrder = parseInt(sort_order, 10) || 0;
        
        // 处理show字段，确保是数字1或0
        let showValue = 1; // 默认显示
        if (show === 'False' || show === 'false' || show === '0' || show === 0 || show === false) {
            showValue = 0;
        }
        
        // 处理lang字段，确保是数字0或1
        let langValue = 0; // 默认中文
        if (lang === 1 || lang === '1') {
            langValue = 1; // 英文
        }
        
        // 使用服务器当前时间作为创建时间（转换为北京时间）
        const now = new Date();
        // 北京时间比UTC时间早8小时，所以加上8小时的毫秒数
        const beijingTime = now.getTime() + (8 * 60 * 60 * 1000);
        
        console.log(`处理后的值: sort_order=${sortOrder}, show=${showValue}, lang=${langValue}, create_time=${beijingTime}`);
        
        const sql = 'INSERT INTO product_recommend (title, description, icon, url, sort_order, `show`, lang, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, FROM_UNIXTIME(? / 1000), FROM_UNIXTIME(? / 1000))';
        db.query(sql, [title, description, icon, url, sortOrder, showValue, langValue, beijingTime, beijingTime], (err, result) => {
            if (err) {
                console.error('创建产品推荐错误:', err);
                return res.json({ status: 'error', msg: '创建数据失败' });
            }
            
            console.log(`成功创建产品推荐，ID=${result.insertId}，标题=${title}，显示状态=${showValue}，语言=${langValue}，排序=${sortOrder}`);
            
            return res.json({
                status: 'ok',
                msg: '创建成功',
                id: result.insertId
            });
        });
    } catch (error) {
        console.error('创建产品推荐错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 更新产品推荐
app.post('/apis/update_product_recommend/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { title, description, icon, url, sort_order, show, lang, filters } = req.body;
        
        console.log('收到更新产品推荐请求:', {
            title,
            description,
            icon,
            url,
            sort_order,
            show,
            lang,
            filters
        });
        
        // 确保sort_order是整数
        const sortOrder = parseInt(sort_order, 10);
        if (isNaN(sortOrder)) {
            return res.json({ status: 'error', msg: '排序必须为整数' });
        }
        
        // 处理show字段，确保是数字1或0
        let showValue = 1; // 默认显示
        if (show === 'False' || show === 'false' || show === '0' || show === 0 || show === false) {
            showValue = 0;
        }
        
        // 处理lang字段，确保是数字0或1
        let langValue = 0; // 默认中文
        if (lang === 1 || lang === '1') {
            langValue = 1; // 英文
        }
        
        // 使用服务器当前时间作为更新时间（转换为北京时间）
        const now = new Date();
        // 北京时间比UTC时间早8小时，所以加上8小时的毫秒数
        const beijingTime = now.getTime() + (8 * 60 * 60 * 1000);
        
        console.log(`处理后的值: sort_order=${sortOrder}, show=${showValue}, lang=${langValue}, update_time=${beijingTime}`);
        
        let filtersObj = {};
        if (filters) {
            try {
                filtersObj = JSON.parse(filters);
                console.log('解析后的filters:', filtersObj);
            } catch (e) {
                console.error('解析filters参数失败:', e);
                return res.json({ status: 'error', msg: '参数格式错误' });
            }
        }
        
        if (!filtersObj.id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        
        const sql = 'UPDATE product_recommend SET title = ?, description = ?, icon = ?, url = ?, sort_order = ?, `show` = ?, lang = ?, update_time = FROM_UNIXTIME(? / 1000) WHERE id = ?';
        db.query(sql, [title, description, icon, url, sortOrder, showValue, langValue, beijingTime, filtersObj.id], (err, result) => {
            if (err) {
                console.error('更新产品推荐错误:', err);
                return res.json({ status: 'error', msg: '更新数据失败' });
            }
            
            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到要更新的数据' });
            }
            
            // 记录操作日志
            console.log(`成功更新ID=${filtersObj.id}的产品推荐，标题=${title}，显示状态=${showValue}，语言=${langValue}，排序=${sortOrder}`);
            
            return res.json({
                status: 'ok',
                msg: '更新成功'
            });
        });
    } catch (error) {
        console.error('更新产品推荐错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 删除产品推荐
app.post('/apis/delete_product_recommend/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { filters } = req.body;
        let filtersObj = {};
        if (filters) {
            try {
                filtersObj = JSON.parse(filters);
            } catch (e) {
                console.error('解析filters参数失败:', e);
                return res.json({ status: 'error', msg: '参数格式错误' });
            }
        }
        if (!filtersObj.id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        // 先查询产品信息
        db.query('SELECT title, icon FROM product_recommend WHERE id = ?', [filtersObj.id], (err, results) => {
            if (err) {
                console.error('查询产品信息错误:', err);
                return res.json({ status: 'error', msg: '查询数据失败' });
            }
            if (results.length === 0) {
                return res.json({ status: 'error', msg: '未找到要删除的数据' });
            }
            const productInfo = results[0];
            const iconPath = productInfo.icon;
            // 删除数据库记录
            db.query('DELETE FROM product_recommend WHERE id = ?', [filtersObj.id], (err, result) => {
                if (err) {
                    console.error('删除产品推荐错误:', err);
                    return res.json({ status: 'error', msg: '删除数据失败' });
                }
                // 尝试删除图标文件（如果是本地存储的）
                if (iconPath && iconPath.startsWith('/uploads/')) {
                    const fullPath = path.join(__dirname, 'public', iconPath);
                    // 在删除前记录删除操作
                    try {
                        const filename = path.basename(fullPath);
                        logMediaOperation('删除', filename, fullPath);
                        // 额外备份一份图片到admin/media，文件名加deleted_时间戳_前缀
                        if (fs.existsSync(fullPath)) {
                            const deletedName = `deleted_${Date.now()}_${filename}`;
                            const deletedBackupPath = path.join(__dirname, 'admin/media', deletedName);
                            fs.copyFileSync(fullPath, deletedBackupPath);
                        }
                    } catch (statErr) {
                        console.error('记录删除操作或备份失败:', statErr);
                    }
                    if (fs.existsSync(fullPath)) {
                        // 删除原始文件
                        fs.unlink(fullPath, (err) => {
                            if (err) {
                                console.error('删除图标文件失败:', err);
                            }
                        });
                    }
                }
                return res.json({
                    status: 'ok',
                    msg: '删除成功'
                });
            });
        });
    } catch (error) {
        console.error('删除产品推荐错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 更新产品推荐显示状态
app.post('/apis/update_product_recommend_status/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { filters, show, lang } = req.body;
        
        console.log('收到更新产品推荐显示状态请求:', {
            filters,
            show,
            lang
        });
        
        // 处理show字段，确保是数字1或0
        let showValue = 1; // 默认显示
        if (show === 'False' || show === 'false' || show === '0' || show === 0 || show === false) {
            showValue = 0;
        }
        
        // 使用服务器当前时间作为更新时间（转换为北京时间）
        const now = new Date();
        // 北京时间比UTC时间早8小时，所以加上8小时的毫秒数
        const beijingTime = now.getTime() + (8 * 60 * 60 * 1000);
        
        let filtersObj = {};
        if (filters) {
            try {
                filtersObj = JSON.parse(filters);
                console.log('解析后的filters:', filtersObj);
            } catch (e) {
                console.error('解析filters参数失败:', e);
                return res.json({ status: 'error', msg: '参数格式错误' });
            }
        }
        
        if (!filtersObj.id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        
        // 先获取当前记录的lang值
        db.query('SELECT lang FROM product_recommend WHERE id = ?', [filtersObj.id], (err, results) => {
            if (err) {
                console.error('查询产品推荐语言错误:', err);
                return res.json({ status: 'error', msg: '查询数据失败' });
            }
            
            if (results.length === 0) {
                return res.json({ status: 'error', msg: '未找到要更新的数据' });
            }
            
            // 获取当前的lang值
            const currentLang = results[0].lang !== undefined ? results[0].lang : 0;
            
            // 如果提供了新的lang值，使用新值，否则保留当前值
            const langValue = lang !== undefined ? (parseInt(lang, 10) || 0) : currentLang;
            
            console.log(`更新ID=${filtersObj.id}的显示状态: show=${showValue}, lang=${langValue}`);
            
            const sql = 'UPDATE product_recommend SET `show` = ?, lang = ?, update_time = FROM_UNIXTIME(? / 1000) WHERE id = ?';
            db.query(sql, [showValue, langValue, beijingTime, filtersObj.id], (err, result) => {
                if (err) {
                    console.error('更新显示状态错误:', err);
                    return res.json({ status: 'error', msg: '更新数据失败' });
                }
                
                if (result.affectedRows === 0) {
                    return res.json({ status: 'error', msg: '未找到要更新的数据' });
                }
                
                // 记录操作日志
                console.log(`成功更新ID=${filtersObj.id}的产品推荐显示状态为${showValue}，语言为${langValue}`);
                
                return res.json({
                    status: 'ok',
                    msg: '更新成功'
                });
            });
        });
    } catch (error) {
        console.error('更新显示状态错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 产品列表相关API
// 获取产品列表
app.get('/apis/product_list/', (req, res) => {
    try {
        // 获取分页参数
        const page = parseInt(req.query.page) || 1;
        const size = parseInt(req.query.size) || 10;
        const offset = (page - 1) * size;
        
        // 构建查询条件
        let whereClause = '1=1';
        let params = [];
        
        // 处理filters参数
        if (req.query.filters) {
            try {
                const filters = JSON.parse(req.query.filters);
                
                if (filters.lang !== undefined && filters.lang !== '') {
                    whereClause += ' AND lang = ?';
                    params.push(filters.lang);
                }
                
                if (filters.info_type !== undefined && filters.info_type !== '') {
                    whereClause += ' AND info_type = ?';
                    params.push(filters.info_type);
                }
                
                if (filters.show !== undefined) {
                    whereClause += ' AND `show` = ?';
                    params.push(filters.show ? 1 : 0);
                }
            } catch (e) {
                console.error('解析filters参数失败:', e);
            }
        } else {
            // 兼容旧的参数方式
            if (req.query.lang !== undefined && req.query.lang !== '') {
                whereClause += ' AND lang = ?';
                params.push(req.query.lang);
            }
            
            if (req.query.info_type !== undefined && req.query.info_type !== '') {
                whereClause += ' AND info_type = ?';
                params.push(req.query.info_type);
            }
        }
        
        // 检查请求来源
        const isAdminRequest = 
            req.headers['x-client-type'] === 'admin' || 
            req.headers['x-admin-request'] === 'true' ||
            (req.headers['user-agent'] && req.headers['user-agent'].includes('admin')) ||
            req.headers['referer'] && (req.headers['referer'].includes('/admin/') || req.headers['referer'].includes('/admin1/'));
        
        // 前端请求时默认只返回显示状态为true的产品
        if (!isAdminRequest) {
            whereClause += ' AND `show` = 1';
        }
        
        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM products WHERE ${whereClause}`;
        
        db.query(countSql, params, (err, countResult) => {
            if (err) {
                console.error('查询产品列表总数失败:', err);
                return res.json({
                    status: 'error',
                    msg: '查询产品列表总数失败'
                });
            }
            
            const total = countResult[0].total;
            
            // 查询分页数据
            const listSql = `
                SELECT * FROM products 
                WHERE ${whereClause}
                ORDER BY lang ASC, display_order ASC, id DESC
                LIMIT ? OFFSET ?
            `;
            
            db.query(listSql, [...params, size, offset], (err, results) => {
                if (err) {
                    console.error('查询产品列表失败:', err);
                    return res.json({
                        status: 'error',
                        msg: '查询产品列表失败'
                    });
                }
                
                // 处理图片路径
                results.forEach(item => {
                    if (item.image) {
                        // 处理绝对路径问题
                        if (item.image.includes('C:')) {
                            // 如果是Windows绝对路径，提取文件名
                            const fileName = item.image.split('\\').pop().split('/').pop();
                            item.image = '/uploads/' + fileName;
                        }
                        // 确保图片路径正确
                        else if (!item.image.startsWith('http') && !item.image.startsWith('/')) {
                            item.image = '/' + item.image;
                        }
                    }
                });
                
                res.json({
                    status: 'ok',
                    total: total,
                    data: results
                });
            });
        });
    } catch (error) {
        console.error('获取产品列表错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 获取产品详情
app.get('/apis/product_detail/', (req, res) => {
    try {
        const id = req.query.id;
        
        if (!id) {
            return res.json({
                status: 'error',
                msg: '缺少ID参数'
            });
        }
        
        const sql = 'SELECT * FROM products WHERE id = ?';
        
        db.query(sql, [id], (err, results) => {
            if (err) {
                console.error('查询产品详情失败:', err);
                return res.json({
                    status: 'error',
                    msg: '查询产品详情失败'
                });
            }
            
            if (results.length === 0) {
                return res.json({
                    status: 'error',
                    msg: '未找到对应的产品'
                });
            }
            
            // 处理图片路径
            if (results[0].image) {
                if (results[0].image.includes('C:')) {
                    const fileName = results[0].image.split('\\').pop().split('/').pop();
                    results[0].image = '/uploads/' + fileName;
                }
                else if (!results[0].image.startsWith('http') && !results[0].image.startsWith('/')) {
                    results[0].image = '/' + results[0].image;
                }
            }
            
            // 为了兼容前端，添加title和content字段
            results[0].title = results[0].name;
            results[0].content = results[0].description;
            results[0].image_path = results[0].image;
            
            res.json({
                status: 'ok',
                data: results[0]
            });
        });
    } catch (error) {
        console.error('获取产品详情错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 添加产品
app.post('/apis/add_product/', upload.single('image'), (req, res) => {
    try {
        const name = req.body.title === '' ? null : req.body.title;
        const infoType = req.body.info_type;
        const show = req.body.show === 'true' || req.body.show === true;
        const lang = parseInt(req.body.lang) || 0;
        let description = req.body.content === '' ? null : req.body.content;
        let imagePath = null;
        const displayOrder = parseInt(req.body.display_order) || 100;
        const url = req.body.url === '' ? null : req.body.url;
        
        console.log('添加产品请求参数:', {
            name, infoType, show, lang, description, displayOrder, url,
            hasFile: !!req.file
        });
        
        // 验证必填字段
        if (!infoType) {
            return res.json({
                status: 'error',
                msg: '产品类型不能为空'
            });
        }
        
        // 处理图片上传
        if (req.file) {
            // 备份图片
            const backupPath = backupMedia(req.file.path, req.file.originalname);
            
            // 记录图片操作日志
            logMediaOperation('上传', req.file.originalname, req.file.path);
            
            // 设置图片路径
            imagePath = '/uploads/' + req.file.filename;
            console.log('新图片路径:', imagePath);
        }
        
        // 插入数据
        const sql = `
            INSERT INTO products (name, description, info_type, image, \`show\`, lang, created_at, update_time, display_order, url)
            VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW(), ?, ?)
        `;
        
        console.log('执行SQL插入:', {
            name, description, infoType, imagePath, show, lang, displayOrder, url
        });
        
        db.query(sql, [name, description, infoType, imagePath, show, lang, displayOrder, url], (err, result) => {
            if (err) {
                console.error('添加产品失败:', err);
                return res.json({
                    status: 'error',
                    msg: '添加产品失败: ' + err.message
                });
            }
            
            console.log('添加产品成功:', result);
            
            res.json({
                status: 'ok',
                msg: '添加产品成功',
                id: result.insertId
            });
        });
    } catch (error) {
        console.error('添加产品错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 更新产品
app.post('/apis/update_product/', upload.single('image'), (req, res) => {
    try {
        const id = req.body.id;
        const name = req.body.title === '' ? null : req.body.title; // 使用title作为name
        const infoType = req.body.info_type;  // 恢复使用info_type字段
        const show = req.body.show === 'true' || req.body.show === true;
        const lang = parseInt(req.body.lang) || 0;
        let description = req.body.content === '' ? null : req.body.content; // 使用content作为description
        let imagePath = null;
        const displayOrder = parseInt(req.body.display_order) || 100;
        const removeImage = req.body.remove_image === 'true';
        const url = req.body.url === '' ? null : req.body.url;
        
        console.log('更新产品请求参数:', {
            id, name, infoType, show, lang, description, displayOrder, removeImage, url,
            hasFile: !!req.file
        });
        
        // 表单验证
        if (!id) {
            return res.json({
                status: 'error',
                msg: '缺少ID参数'
            });
        }
        
        if (!infoType) {
            return res.json({
                status: 'error',
                msg: '产品类型不能为空'
            });
        }
        
        // 获取原始数据
        db.query('SELECT * FROM products WHERE id = ?', [id], (err, results) => {
            if (err) {
                console.error('查询原始产品数据失败:', err);
                return res.json({
                    status: 'error',
                    msg: '更新产品失败'
                });
            }
            
            if (results.length === 0) {
                return res.json({
                    status: 'error',
                    msg: '未找到对应的产品'
                });
            }
            
            const originalProduct = results[0];
            console.log('原始产品数据:', originalProduct);
            
            // 处理图片上传
            if (req.file) {
                // 如果上传了新图片
                
                // 备份原图片
                if (originalProduct.image && originalProduct.image.startsWith('/')) {
                    try {
                        const originalPath = path.join(__dirname, 'public', originalProduct.image);
                        if (fs.existsSync(originalPath)) {
                            const backupPath = backupMedia(originalPath, path.basename(originalPath));
                            logMediaOperation('备份', path.basename(originalPath), backupPath);
                        }
                    } catch (err) {
                        console.error('备份原图片失败:', err);
                    }
                }
                
                // 备份新图片
                const backupPath = backupMedia(req.file.path, req.file.originalname);
                
                // 记录图片操作日志
                logMediaOperation('更新', req.file.originalname, req.file.path);
                
                // 设置图片路径
                imagePath = '/uploads/' + req.file.filename;
                console.log('新图片路径:', imagePath);
            } else if (removeImage) {
                // 如果请求清除图片
                
                // 如果原内容是图片路径，先备份
                if (originalProduct.image && originalProduct.image.startsWith('/')) {
                    try {
                        const originalPath = path.join(__dirname, 'public', originalProduct.image);
                        if (fs.existsSync(originalPath)) {
                            const backupPath = backupMedia(originalPath, path.basename(originalPath));
                            logMediaOperation('清除前备份', path.basename(originalPath), backupPath);
                        }
                    } catch (err) {
                        console.error('备份原图片失败:', err);
                    }
                }
                
                // 清空图片内容
                imagePath = null;
                console.log('清除图片路径');
                
            } else {
                // 没有上传新图片且没有清除图片，保持原图片路径
                imagePath = originalProduct.image;
                console.log('保持原图片路径:', imagePath);
            }
            
            // 如果没有提供新的文本内容，保持原文本内容
            if (description === undefined) {
                description = originalProduct.description;
                console.log('使用原文本内容:', description);
            } else {
                console.log('使用新文本内容:', description);
            }
            
            // 更新数据
            const sql = `
                UPDATE products 
                SET name = ?, description = ?, info_type = ?, image = ?, \`show\` = ?, lang = ?, display_order = ?, url = ?, update_time = NOW()
                WHERE id = ?
            `;
            
            console.log('执行SQL更新:', {
                name, description, infoType, imagePath, show, lang, displayOrder, url, id
            });
            
            db.query(sql, [name, description, infoType, imagePath, show, lang, displayOrder, url, id], (err, result) => {
                if (err) {
                    console.error('更新产品失败:', err);
                    return res.json({
                        status: 'error',
                        msg: '更新产品失败: ' + err.message
                    });
                }
                
                console.log('更新产品成功:', result);
                
                res.json({
                    status: 'ok',
                    msg: '更新产品成功'
                });
            });
        });
    } catch (error) {
        console.error('更新产品错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 删除产品
app.post('/apis/delete_product/', (req, res) => {
    try {
        const id = req.body.id;
        
        if (!id) {
            return res.json({
                status: 'error',
                msg: '缺少ID参数'
            });
        }
        
        // 获取原始数据
        db.query('SELECT * FROM products WHERE id = ?', [id], (err, results) => {
            if (err) {
                console.error('查询原始产品数据失败:', err);
                return res.json({
                    status: 'error',
                    msg: '删除产品失败'
                });
            }
            
            if (results.length === 0) {
                return res.json({
                    status: 'error',
                    msg: '未找到对应的产品'
                });
            }
            
            const originalProduct = results[0];
            
            // 如果有图片，备份图片
            if (originalProduct.image && originalProduct.image.startsWith('/')) {
                const originalPath = path.join(__dirname, 'public', originalProduct.image);
                if (fs.existsSync(originalPath)) {
                    const backupPath = backupMedia(originalPath, path.basename(originalPath));
                    logMediaOperation('删除前备份', path.basename(originalPath), backupPath);
                }
            }
            
            // 删除数据
            db.query('DELETE FROM products WHERE id = ?', [id], (err, result) => {
                if (err) {
                    console.error('删除产品失败:', err);
                    return res.json({
                        status: 'error',
                        msg: '删除产品失败'
                    });
                }
                
                res.json({
                    status: 'ok',
                    msg: '删除产品成功'
                });
            });
        });
    } catch (error) {
        console.error('删除产品错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 更新产品显示顺序
app.post('/apis/update_product_order/', (req, res) => {
    try {
        const { id, display_order } = req.body;
        
        if (!id) {
            return res.json({
                status: 'error',
                msg: '缺少ID参数'
            });
        }
        
        // 确保display_order是整数
        const orderValue = parseInt(display_order, 10);
        if (isNaN(orderValue)) {
            return res.json({
                status: 'error',
                msg: '显示顺序必须为整数'
            });
        }
        
        const sql = 'UPDATE products SET display_order = ? WHERE id = ?';
        
        db.query(sql, [orderValue, id], (err, result) => {
            if (err) {
                console.error('更新产品显示顺序失败:', err);
                return res.json({
                    status: 'error',
                    msg: '更新显示顺序失败'
                });
            }
            
            if (result.affectedRows === 0) {
                return res.json({
                    status: 'error',
                    msg: '未找到对应的产品'
                });
            }
            
            res.json({
                status: 'ok',
                msg: '更新显示顺序成功'
            });
        });
    } catch (error) {
        console.error('更新产品显示顺序错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 切换产品显示状态
app.post('/apis/products/toggle_show', (req, res) => {
    try {
        const { id, show } = req.body;
        
        console.log('收到切换产品显示状态请求:', {
            id, show
        });
        
        if (!id) {
            return res.json({
                status: 'error',
                msg: '缺少ID参数'
            });
        }
        
        // 处理show字段，确保是布尔值
        const showValue = show === 'true' || show === true || show === 1 || show === '1';
        
        // 只更新show字段，注意这里使用的是数据库中实际的表结构
        const sql = 'UPDATE products SET `show` = ? WHERE id = ?';
        
        db.query(sql, [showValue, id], (err, result) => {
            if (err) {
                console.error('更新产品显示状态失败:', err);
                return res.json({
                    status: 'error',
                    msg: '更新显示状态失败'
                });
            }
            
            if (result.affectedRows === 0) {
                return res.json({
                    status: 'error',
                    msg: '未找到对应的产品'
                });
            }
            
            console.log(`成功更新ID=${id}的显示状态为${showValue}`);
            
            res.json({
                status: 'ok',
                msg: '更新显示状态成功'
            });
        });
    } catch (error) {
        console.error('更新产品显示状态错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 产品推荐操作日志API
app.post('/apis/log_product_recommend/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { operation, title, icon, description, url, sort_order, show, time } = req.body;
        const logFile = path.join(__dirname, 'admin/media/product_recommend_log.txt');
        const logEntry = `[${time || new Date().toISOString()}] [${operation}] [${title || ''}] [${icon || ''}] [${description || ''}] [${url || ''}] [${sort_order || ''}] [${show || ''}]
`;
        fs.appendFileSync(logFile, logEntry, 'utf8');
        res.json({ status: 'ok', msg: '日志已记录' });
    } catch (e) {
        console.error('记录产品推荐操作日志失败:', e);
        res.json({ status: 'error', msg: '日志记录失败' });
    }
});

// 公司详情管理API
// 获取公司详情列表
app.get('/apis/company_info/list', (req, res) => {
    
    // 获取分页参数
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 10;
    const offset = (page - 1) * size;
    
    // 构建查询条件
    let whereClause = '1=1';
    let params = [];
    
    // 处理filters参数
    if (req.query.filters) {
        try {
            const filters = JSON.parse(req.query.filters);

            
            if (filters.lang !== undefined && filters.lang !== '') {
                whereClause += ' AND lang = ?';
                params.push(filters.lang);
            }
            
            if (filters.info_type !== undefined && filters.info_type !== '') {
                whereClause += ' AND info_type = ?';
                params.push(filters.info_type);
            }
            
            if (filters.show !== undefined) {
                whereClause += ' AND `show` = ?';
                params.push(filters.show ? 1 : 0);
            }
        } catch (e) {
            console.error('解析filters参数失败:', e);
        }
    } else {
        // 兼容旧的参数方式
        if (req.query.lang !== undefined && req.query.lang !== '') {
            whereClause += ' AND lang = ?';
            params.push(req.query.lang);
        }
        
        if (req.query.info_type !== undefined && req.query.info_type !== '') {
            whereClause += ' AND info_type = ?';
            params.push(req.query.info_type);
        }
    }
    

    
    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM company_info WHERE ${whereClause}`;
    
    db.query(countSql, params, (err, countResult) => {
        if (err) {
            console.error('查询公司详情总数失败:', err);
            return res.json({
                status: 'error',
                msg: '查询公司详情总数失败'
            });
        }
        
        const total = countResult[0].total;
        
        // 查询分页数据
        const listSql = `
            SELECT * FROM company_info 
            WHERE ${whereClause}
            ORDER BY lang ASC, display_order ASC, id DESC
            LIMIT ? OFFSET ?
        `;
        
        db.query(listSql, [...params, size, offset], (err, results) => {
            if (err) {
                console.error('查询公司详情列表失败:', err);
                return res.json({
                    status: 'error',
                    msg: '查询公司详情列表失败'
                });
            }
            
            // 处理图片路径
            results.forEach(item => {
                if (item.info_type === 'company_logo' && item.content) {
                    // 处理绝对路径问题
                    if (item.content.includes('C:')) {
                        // 如果是Windows绝对路径，提取文件名
                        const fileName = item.content.split('\\').pop().split('/').pop();
                        item.content = '/uploads/' + fileName;
                    }
                    // 确保图片路径正确
                    else if (!item.content.startsWith('http') && !item.content.startsWith('/')) {
                        item.content = '/' + item.content;
                    }
                }
            });
            
            res.json({
                status: 'ok',
                total: total,
                data: results
            });
        });
    });
});

// 获取公司详情详情
app.get('/apis/company_info/detail', (req, res) => {
    
    const id = req.query.id;
    
    if (!id) {
        return res.json({
            status: 'error',
            msg: '缺少ID参数'
        });
    }
    
    const sql = 'SELECT * FROM company_info WHERE id = ?';
    
    db.query(sql, [id], (err, results) => {
        if (err) {
            console.error('查询公司详情详情失败:', err);
            return res.json({
                status: 'error',
                msg: '查询公司详情详情失败'
            });
        }
        
        if (results.length === 0) {
            return res.json({
                status: 'error',
                msg: '未找到对应的公司详情'
            });
        }
        
        res.json({
            status: 'ok',
            data: results[0]
        });
    });
});

// 添加公司详情
app.post('/apis/company_info/add', upload.single('image'), (req, res) => {
    
    const title = req.body.title === '' ? null : req.body.title;
    const infoType = req.body.info_type;
    const show = req.body.show === 'true' || req.body.show === true;
    const lang = parseInt(req.body.lang) || 0;
    let content = req.body.content === '' ? null : req.body.content;
    let imagePath = null;
    const displayOrder = parseInt(req.body.display_order) || 100;
    const url = req.body.url === '' ? null : req.body.url; // 处理链接地址字段
    
    console.log('添加公司详情请求参数:', {
        title, infoType, show, lang, content, displayOrder, url,
        hasFile: !!req.file
    });
    
    // 移除验证，允许所有字段为空
    if (!infoType) {
        return res.json({
            status: 'error',
            msg: '信息类型不能为空'
        });
    }
    
    // 处理图片上传 - 不限于特定类型
    if (req.file) {
        // 备份图片
        const backupPath = backupMedia(req.file.path, req.file.originalname);
        
        // 记录图片操作日志
        logMediaOperation('上传', req.file.originalname, req.file.path);
        
        // 设置图片路径
        imagePath = '/uploads/' + req.file.filename;
        console.log('新图片路径:', imagePath);
    }
    
    // 插入数据
    const now = Date.now();
    const sql = `
        INSERT INTO company_info (title, content, image_path, info_type, \`show\`, lang, create_time, update_time, display_order, url)
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?)
    `;
    
    console.log('执行SQL插入:', {
        title, content, imagePath, infoType, show, lang, now, displayOrder, url
    });
    
    db.query(sql, [title, content, imagePath, infoType, show, lang, now, displayOrder, url], (err, result) => {
        if (err) {
            console.error('添加公司详情失败:', err);
            return res.json({
                status: 'error',
                msg: '添加公司详情失败: ' + err.message
            });
        }
        
        console.log('添加公司详情成功:', result);
        
        res.json({
            status: 'ok',
            msg: '添加公司详情成功',
            id: result.insertId
        });
    });
});

// 更新公司详情
app.post('/apis/company_info/update', upload.single('image'), (req, res) => {
    
    const id = req.body.id;
    const title = req.body.title === '' ? null : req.body.title;
    const infoType = req.body.info_type;
    const show = req.body.show === 'true' || req.body.show === true;
    const lang = parseInt(req.body.lang) || 0;
    let content = req.body.content === '' ? null : req.body.content;
    let imagePath = null;
    const displayOrder = parseInt(req.body.display_order) || 100;
    const removeImage = req.body.remove_image === 'true';
    const url = req.body.url === '' ? null : req.body.url; // 处理链接地址字段
    
    console.log('更新公司详情请求参数:', {
        id, title, infoType, show, lang, content, displayOrder, removeImage, url,
        hasFile: !!req.file
    });
    
    // 表单验证 - 对Logo类型特殊处理
    if (!id) {
        return res.json({
            status: 'error',
            msg: '缺少ID参数'
        });
    }
    
    // 移除标题验证，允许标题为空
    
    // 获取原始数据
    db.query('SELECT * FROM company_info WHERE id = ?', [id], (err, results) => {
        if (err) {
            console.error('查询原始公司详情失败:', err);
            return res.json({
                status: 'error',
                msg: '更新公司详情失败'
            });
        }
        
        if (results.length === 0) {
            return res.json({
                status: 'error',
                msg: '未找到对应的公司详情'
            });
        }
        
        const originalInfo = results[0];
        console.log('原始公司详情:', originalInfo);
        
        // 处理图片上传 - 不限于特定类型
        if (req.file) {
            // 如果上传了新图片
            
            // 备份原图片
            if (originalInfo.image_path && originalInfo.image_path.startsWith('/')) {
                try {
                    const originalPath = path.join(__dirname, 'public', originalInfo.image_path);
                    if (fs.existsSync(originalPath)) {
                        const backupPath = backupMedia(originalPath, path.basename(originalPath));
                        logMediaOperation('备份', path.basename(originalPath), backupPath);
                    }
                } catch (err) {
                    console.error('备份原图片失败:', err);
                }
            }
            
            // 备份新图片
            const backupPath = backupMedia(req.file.path, req.file.originalname);
            
            // 记录图片操作日志
            logMediaOperation('更新', req.file.originalname, req.file.path);
            
            // 设置图片路径
            imagePath = '/uploads/' + req.file.filename;
            console.log('新图片路径:', imagePath);
        } else if (removeImage) {
            // 如果请求清除图片
            
            // 如果原内容是图片路径，先备份
            if (originalInfo.image_path && originalInfo.image_path.startsWith('/')) {
                try {
                    const originalPath = path.join(__dirname, 'public', originalInfo.image_path);
                    if (fs.existsSync(originalPath)) {
                        const backupPath = backupMedia(originalPath, path.basename(originalPath));
                        logMediaOperation('清除前备份', path.basename(originalPath), backupPath);
                    }
                } catch (err) {
                    console.error('备份原图片失败:', err);
                }
            }
            
            // 清空图片内容
            imagePath = null;
            console.log('清除图片路径');
            
        } else {
            // 没有上传新图片且没有清除图片，保持原图片路径
            imagePath = originalInfo.image_path;
            console.log('保持原图片路径:', imagePath);
        }
        
        // 如果没有提供新的文本内容，保持原文本内容
        if (content === undefined) {
            content = originalInfo.content;
            console.log('使用原文本内容:', content);
        } else {
            console.log('使用新文本内容:', content);
        }
        
        // 更新数据 - 确保始终更新时间戳
        const sql = `
            UPDATE company_info 
            SET title = ?, content = ?, image_path = ?, info_type = ?, \`show\` = ?, lang = ?, display_order = ?, url = ?, update_time = NOW()
            WHERE id = ?
        `;
        
        console.log('执行SQL更新:', {
            title, content, imagePath, infoType, show, lang, displayOrder, url, id
        });
        
        db.query(sql, [title, content, imagePath, infoType, show, lang, displayOrder, url, id], (err, result) => {
            if (err) {
                console.error('更新公司详情失败:', err);
                return res.json({
                    status: 'error',
                    msg: '更新公司详情失败: ' + err.message
                });
            }
            
            console.log('更新公司详情成功:', result);
            
            res.json({
                status: 'ok',
                msg: '更新公司详情成功'
            });
        });
    });
});

// 删除公司详情
app.post('/apis/company_info/delete', (req, res) => {
    
    const id = req.body.id;
    
    if (!id) {
        return res.json({
            status: 'error',
            msg: '缺少ID参数'
        });
    }
    
    // 获取原始数据
    db.query('SELECT * FROM company_info WHERE id = ?', [id], (err, results) => {
        if (err) {
            console.error('查询原始公司详情失败:', err);
            return res.json({
                status: 'error',
                msg: '删除公司详情失败'
            });
        }
        
        if (results.length === 0) {
            return res.json({
                status: 'error',
                msg: '未找到对应的公司详情'
            });
        }
        
        const originalInfo = results[0];
        
        // 如果是图片类型，备份图片
        if (originalInfo.info_type === 'company_logo' && originalInfo.content && originalInfo.content.startsWith('/')) {
            const originalPath = path.join(__dirname, originalInfo.content.substring(1));
            if (fs.existsSync(originalPath)) {
                const backupPath = backupMedia(originalPath, path.basename(originalPath));
                logMediaOperation('删除前备份', path.basename(originalPath), backupPath);
            }
        }
        
        // 删除数据
        db.query('DELETE FROM company_info WHERE id = ?', [id], (err, result) => {
            if (err) {
                console.error('删除公司详情失败:', err);
                return res.json({
                    status: 'error',
                    msg: '删除公司详情失败'
                });
            }
            
            res.json({
                status: 'ok',
                msg: '删除公司详情成功'
            });
        });
    });
});

// 公司详情操作日志API
app.post('/apis/log_company_info/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { operation, title, info_type, content, show, lang, time } = req.body;
        const logFile = path.join(__dirname, 'admin/media/company_info_log.txt');
        const logEntry = `[${time || new Date().toISOString()}] [${operation}] [${title || ''}] [${info_type || ''}] [${content ? (content.length > 50 ? content.substring(0, 50) + '...' : content) : ''}] [${show || ''}] [${lang === 0 ? '中文' : '英文'}]
`;
        fs.appendFileSync(logFile, logEntry, 'utf8');
        res.json({ status: 'ok', msg: '日志已记录' });
    } catch (e) {
        console.error('记录公司详情操作日志失败:', e);
        res.json({ status: 'error', msg: '日志记录失败' });
    }
});

// 更新公司详情显示顺序
app.post('/apis/company_info/update_order', (req, res) => {
    
    const { id, display_order } = req.body;
    
    if (!id) {
        return res.json({
            status: 'error',
            msg: '缺少ID参数'
        });
    }
    
    // 确保display_order是整数
    const orderValue = parseInt(display_order, 10);
    if (isNaN(orderValue)) {
        return res.json({
            status: 'error',
            msg: '显示顺序必须为整数'
        });
    }
    
    const sql = 'UPDATE company_info SET display_order = ? WHERE id = ?';
    
    db.query(sql, [orderValue, id], (err, result) => {
        if (err) {
            console.error('更新公司详情显示顺序失败:', err);
            return res.json({
                status: 'error',
                msg: '更新显示顺序失败'
            });
        }
        
        if (result.affectedRows === 0) {
            return res.json({
                status: 'error',
                msg: '未找到对应的公司详情'
            });
        }
        

        
        res.json({
            status: 'ok',
            msg: '更新显示顺序成功'
        });
    });
});

// 仅更新公司详情显示状态
app.post('/apis/company_info/updateShowStatusOnly', (req, res) => {
    const { id, show } = req.body;
    
    console.log('收到更新显示状态请求:', {
        id, show
    });
    
    if (!id) {
        return res.json({
            status: 'error',
            msg: '缺少ID参数'
        });
    }
    
    // 处理show字段，确保是布尔值
    const showValue = show === 'true' || show === true || show === 1 || show === '1';
    
    // 只更新show字段，使用直接的SQL语句
    const sql = 'UPDATE company_info SET `show` = ? WHERE id = ?';
    
    db.query(sql, [showValue, id], (err, result) => {
        if (err) {
            console.error('更新显示状态失败:', err);
            return res.json({
                status: 'error',
                msg: '更新显示状态失败'
            });
        }
        
        if (result.affectedRows === 0) {
            return res.json({
                status: 'error',
                msg: '未找到对应的公司详情'
            });
        }
        
        console.log(`成功更新ID=${id}的显示状态为${showValue}`);
        
        res.json({
            status: 'ok',
            msg: '更新显示状态成功'
        });
    });
});

// 批量更新公司详情显示顺序
app.post('/apis/company_info/batch_update_order', (req, res) => {
    
    const { items } = req.body;
    
    if (!items || !Array.isArray(items) || items.length === 0) {
        return res.json({
            status: 'error',
            msg: '缺少有效的排序数据'
        });
    }
    
    // 验证每个项目的格式
    for (const item of items) {
        if (!item.id || typeof item.display_order !== 'number') {
            return res.json({
                status: 'error',
                msg: '排序数据格式不正确，每项必须包含id和display_order'
            });
        }
    }
    
    // 使用事务来确保所有更新都成功或都失败
    db.beginTransaction(err => {
        if (err) {
            console.error('开始事务失败:', err);
            return res.json({
                status: 'error',
                msg: '批量更新失败'
            });
        }
        
        let completed = 0;
        let failed = false;
        
        // 对每个项目执行更新
        for (const item of items) {
            db.query(
                'UPDATE company_info SET display_order = ? WHERE id = ?',
                [item.display_order, item.id],
                (err, result) => {
                    if (failed) return; // 如果已经失败，不再继续处理
                    
                    if (err) {
                        failed = true;
                        console.error(`更新ID=${item.id}的显示顺序失败:`, err);
                        db.rollback(() => {
                            res.json({
                                status: 'error',
                                msg: `更新ID=${item.id}的显示顺序失败`
                            });
                        });
                        return;
                    }
                    
                    completed++;
                    
                    // 所有更新都完成后提交事务
                    if (completed === items.length) {
                        db.commit(err => {
                            if (err) {
                                console.error('提交事务失败:', err);
                                db.rollback(() => {
                                    res.json({
                                        status: 'error',
                                        msg: '提交更新失败'
                                    });
                                });
                                return;
                            }
                            

                            res.json({
                                status: 'ok',
                                msg: `成功更新${completed}个项目的显示顺序`
                            });
                        });
                    }
                }
            );
        }
    });
});

// 获取按显示顺序排序的公司详情
app.get('/apis/company_info/sorted', (req, res) => {
    
    // 获取查询参数
    const { info_type, lang, show_only } = req.query;
    
    // 构建查询条件
    let whereClause = '1=1';
    let params = [];
    
    if (info_type !== undefined && info_type !== '') {
        whereClause += ' AND info_type = ?';
        params.push(info_type);
    }
    
    if (lang !== undefined && lang !== '') {
        whereClause += ' AND lang = ?';
        params.push(parseInt(lang, 10));
    }
    
    // 如果指定了只显示已启用的项目
    if (show_only === 'true' || show_only === '1') {
        whereClause += ' AND `show` = 1';
    }
    
    // 查询数据并按display_order排序
    const sql = `
        SELECT * FROM company_info 
        WHERE ${whereClause}
        ORDER BY display_order ASC, id DESC
    `;
    
    db.query(sql, params, (err, results) => {
        if (err) {
            console.error('查询排序后的公司详情失败:', err);
            return res.json({
                status: 'error',
                msg: '查询公司详情失败'
            });
        }
        
        // 处理图片路径
        results.forEach(item => {
            if (item.info_type === 'company_logo' && item.content) {
                // 处理绝对路径问题
                if (item.content.includes('C:')) {
                    // 如果是Windows绝对路径，提取文件名
                    const fileName = item.content.split('\\').pop().split('/').pop();
                    item.content = '/uploads/' + fileName;
                }
                // 确保图片路径正确
                else if (!item.content.startsWith('http') && !item.content.startsWith('/')) {
                    item.content = '/' + item.content;
                }
            }
        });
        
        res.json({
            status: 'ok',
            data: results
        });
    });
});

// 获取合作伙伴列表
app.post('/apis/get_partner_pic/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { page = 1, page_size = 10, filters } = req.body;
        
        let filtersObj = {};
        if (filters) {
            try {
                filtersObj = JSON.parse(filters);
            } catch (e) {
                // 解析失败，使用空对象
            }
        }
        
        // 构建WHERE子句
        let whereClause = '1=1';
        const queryParams = [];
        
        if (filtersObj.id) {
            whereClause += ' AND id = ?';
            queryParams.push(filtersObj.id);
        }
        
        // 检查请求来源
        const isAdminRequest = 
            req.headers['x-client-type'] === 'admin' || 
            req.headers['x-admin-request'] === 'true' ||
            (req.headers['user-agent'] && req.headers['user-agent'].includes('admin')) ||
            req.headers['referer'] && (req.headers['referer'].includes('/admin/') || req.headers['referer'].includes('/admin1/'));
        
        // 前端请求时默认只返回显示状态为true的记录
        if (!isAdminRequest) {
            whereClause += ' AND `show` = 1';
        }
        
        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM partner_pics WHERE ${whereClause}`;
        db.query(countSql, queryParams, (err, countResult) => {
            if (err) {
                return res.json({ status: 'error', msg: '查询数据失败' });
            }
            
            const total = countResult[0].total;
            const totalPages = Math.ceil(total / page_size);
            
            // 查询数据
            const offset = (page - 1) * page_size;
            const dataSql = `SELECT * FROM partner_pics WHERE ${whereClause} ORDER BY pic_idx ASC, id ASC LIMIT ? OFFSET ?`;
            
            db.query(dataSql, [...queryParams, parseInt(page_size), offset], (err, results) => {
                if (err) {
                    return res.json({ status: 'error', msg: '查询数据失败' });
                }
                
                return res.json({
                    status: 'ok',
                    data_list: results,
                    page: parseInt(page),
                    page_size: parseInt(page_size),
                    total_data: total,
                    total_pages: totalPages
                });
            });
        });
    } catch (error) {
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 创建合作伙伴
app.post('/apis/create_partner_pic/', upload.single('file'), (req, res) => {
    try {
        if (!req.file) {
            return res.json({ status: 'error', msg: '请上传图片' });
        }
        
        const { url, pic_idx, show } = req.body;
        
        console.log('收到创建合作伙伴请求:', {
            file: req.file,
            url,
            pic_idx,
            show
        });
        
        // 确保pic_idx是整数
        const picIdx = parseInt(pic_idx, 10) || 0;
        
        // 处理show字段，确保是数字1或0
        let showValue = 1; // 默认显示
        if (show === 'False' || show === 'false' || show === '0' || show === 0 || show === false) {
            showValue = 0;
        }
        
        // 生成图片URL
        const picUrl = `/uploads/${req.file.filename}`;
        
        // 备份图片到media目录
        backupMedia(req.file.path, req.file.originalname);
        
        const sql = 'INSERT INTO partner_pics (pic, url, pic_idx, `show`) VALUES (?, ?, ?, ?)';
        db.query(sql, [picUrl, url, picIdx, showValue], (err, result) => {
            if (err) {
                console.error('创建合作伙伴错误:', err);
                return res.json({ status: 'error', msg: '创建数据失败' });
            }
            
            console.log(`成功创建合作伙伴，ID=${result.insertId}，图片=${picUrl}，显示状态=${showValue}，排序=${picIdx}`);
            
            return res.json({
                status: 'ok',
                msg: '创建成功',
                id: result.insertId
            });
        });
    } catch (error) {
        console.error('创建合作伙伴错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 更新合作伙伴
app.post('/apis/update_partner_pic/', upload.single('file'), (req, res) => {
    try {
        const { filters, url, pic_idx, show } = req.body;
        
        console.log('收到更新合作伙伴请求:', {
            file: req.file,
            filters,
            url,
            pic_idx,
            show
        });
        
        if (!filters) {
            return res.json({ status: 'error', msg: '未指定更新条件' });
        }
        
        let filtersObj = {};
        try {
            filtersObj = JSON.parse(filters);
        } catch (e) {
            console.error('解析filters参数失败:', e);
            return res.json({ status: 'error', msg: '更新条件格式错误' });
        }
        
        if (!filtersObj.id) {
            return res.json({ status: 'error', msg: '未指定ID' });
        }
        
        // 确保pic_idx是整数
        const picIdx = parseInt(pic_idx, 10);
        if (isNaN(picIdx)) {
            return res.json({ status: 'error', msg: '排序必须为整数' });
        }
        
        // 处理show字段，确保是数字1或0
        let showValue = 1; // 默认显示
        if (show === 'False' || show === 'false' || show === '0' || show === 0 || show === false) {
            showValue = 0;
        }
        
        // 构建更新字段和参数
        let updateFields = [];
        let updateParams = [];
        
        // 添加url字段
        if (url !== undefined) {
            updateFields.push('url = ?');
            updateParams.push(url);
        }
        
        // 添加pic_idx字段
        updateFields.push('pic_idx = ?');
        updateParams.push(picIdx);
        
        // 添加show字段
        updateFields.push('`show` = ?');
        updateParams.push(showValue);
        
        // 如果上传了新图片，处理图片
        if (req.file) {
            // 生成图片URL
            const picUrl = `/uploads/${req.file.filename}`;
            
            // 备份图片到media目录
            backupMedia(req.file.path, req.file.originalname);
            
            updateFields.push('pic = ?');
            updateParams.push(picUrl);
        }
        
        // 添加ID条件
        updateParams.push(filtersObj.id);
        
        const sql = `UPDATE partner_pics SET ${updateFields.join(', ')} WHERE id = ?`;
        db.query(sql, updateParams, (err, result) => {
            if (err) {
                console.error('更新合作伙伴错误:', err);
                return res.json({ status: 'error', msg: '更新数据失败' });
            }
            
            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到指定记录' });
            }
            
            console.log(`成功更新合作伙伴，ID=${filtersObj.id}，影响行数=${result.affectedRows}`);
            
            return res.json({
                status: 'ok',
                msg: '更新成功',
                affected_rows: result.affectedRows
            });
        });
    } catch (error) {
        console.error('更新合作伙伴错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 删除合作伙伴
app.post('/apis/delete_partner_pic/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { filters } = req.body;
        
        console.log('收到删除合作伙伴请求:', {
            filters
        });
        
        if (!filters) {
            return res.json({ status: 'error', msg: '未指定删除条件' });
        }
        
        let filtersObj = {};
        try {
            filtersObj = JSON.parse(filters);
        } catch (e) {
            console.error('解析filters参数失败:', e);
            return res.json({ status: 'error', msg: '删除条件格式错误' });
        }
        
        if (!filtersObj.id) {
            return res.json({ status: 'error', msg: '未指定ID' });
        }
        
        // 先查询图片路径，用于删除文件
        db.query('SELECT pic FROM partner_pics WHERE id = ?', [filtersObj.id], (err, results) => {
            if (err) {
                console.error('查询图片路径错误:', err);
                return res.json({ status: 'error', msg: '查询数据失败' });
            }
            
            if (results.length === 0) {
                return res.json({ status: 'error', msg: '未找到指定记录' });
            }
            
            const picPath = results[0].pic;
            
            // 删除数据库记录
            db.query('DELETE FROM partner_pics WHERE id = ?', [filtersObj.id], (err, result) => {
                if (err) {
                    console.error('删除合作伙伴错误:', err);
                    return res.json({ status: 'error', msg: '删除数据失败' });
                }
                
                if (result.affectedRows === 0) {
                    return res.json({ status: 'error', msg: '未找到指定记录' });
                }
                
                console.log(`成功删除合作伙伴，ID=${filtersObj.id}，影响行数=${result.affectedRows}`);
                
                // 尝试删除图片文件（如果存在）
                try {
                    const fullPath = path.join(__dirname, 'public', picPath);
                    if (fs.existsSync(fullPath)) {
                        fs.unlinkSync(fullPath);
                        console.log(`已删除图片文件: ${fullPath}`);
                    }
                } catch (e) {
                    console.error('删除图片文件失败:', e);
                    // 继续执行，不影响API返回结果
                }
                
                return res.json({
                    status: 'ok',
                    msg: '删除成功',
                    affected_rows: result.affectedRows
                });
            });
        });
    } catch (error) {
        console.error('删除合作伙伴错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});



// 产品详情相关API
// 获取产品详情列表
app.get('/apis/product_detail_list/', (req, res) => {
    console.log('收到产品详情列表请求');
    try {
        // 获取分页参数
        const page = parseInt(req.query.page) || 1;
        const size = parseInt(req.query.size) || 10;
        const offset = (page - 1) * size;
        
        // 构建查询条件
        let whereClause = '1=1';
        let params = [];
        
        // 处理filters参数
        if (req.query.filters) {
            try {
                const filters = JSON.parse(req.query.filters);
                
                if (filters.lang !== undefined && filters.lang !== '') {
                    whereClause += ' AND lang = ?';
                    params.push(filters.lang);
                }
                
                if (filters.info_type !== undefined && filters.info_type !== '') {
                    whereClause += ' AND info_type = ?';
                    params.push(filters.info_type);
                }
            } catch (e) {
                console.error('解析filters参数失败:', e);
            }
        }
        
        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM products_info WHERE ${whereClause}`;
        db.query(countSql, params, (err, countResult) => {
            if (err) {
                console.error('查询总数失败:', err);
                res.json({ status: 'error', msg: '查询失败' });
                return;
            }
            
            const total = countResult[0].total;
            
            // 查询数据
            const sql = `
                SELECT * FROM products_info 
                WHERE ${whereClause}
                ORDER BY lang ASC, display_order ASC, id DESC 
                LIMIT ? OFFSET ?
            `;
            
            db.query(sql, [...params, size, offset], (err, results) => {
                if (err) {
                    console.error('查询数据失败:', err);
                    res.json({ status: 'error', msg: '查询失败' });
                    return;
                }
                
                res.json({
                    status: 'ok',
                    data: results,
                    total: total
                });
            });
        });
    } catch (e) {
        console.error('获取产品详情列表失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 获取所有产品详情类型（去重）
app.get('/apis/product_detail_types/', (req, res) => {
    db.query('SELECT DISTINCT info_type FROM products_info WHERE info_type IS NOT NULL AND info_type != ""', (err, results) => {
        if (err) return res.json({ status: 'error', msg: '查询失败' });
        const types = results.map(r => r.info_type);
        res.json({ status: 'ok', data: types });
    });
});

// 获取单个产品详情
app.get('/apis/product_info_detail/', (req, res) => {
    try {
        const id = req.query.id;
        if (!id) {
            res.json({ status: 'error', msg: '缺少产品ID' });
            return;
        }
        
        const sql = 'SELECT * FROM products_info WHERE id = ?';
        db.query(sql, [id], (err, results) => {
            if (err) {
                console.error('查询产品详情失败:', err);
                res.json({ status: 'error', msg: '查询失败' });
                return;
            }
            
            if (results.length === 0) {
                res.json({ status: 'error', msg: '产品不存在' });
                return;
            }
            
            res.json({
                status: 'ok',
                data: results[0]
            });
        });
    } catch (e) {
        console.error('获取产品详情失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 更新产品详情
app.post('/apis/update_product_detail/', upload.single('image'), (req, res) => {
    try {
        const id = req.body.id;
        if (!id) {
            res.json({ status: 'error', msg: '缺少产品ID' });
            return;
        }
        
        // 构建更新数据
        const updateData = {
            lang: req.body.lang,
            info_type: req.body.info_type,
            title: req.body.title,
            content: req.body.content,
            url: req.body.url,
            display_order: req.body.display_order || 100,
            [`show`]: (req.body.show === 'True' || req.body.show === true || req.body.show === 1 || req.body.show === '1') ? 1 : 0,
            update_time: new Date()
        };
        
        // 处理图片
        if (req.file) {
            // 如果上传了新图片
            updateData.image_path = '/uploads/' + req.file.filename;
        } else if (req.body.image_path === '') {
            // 如果传递了空的image_path，表示清除图片
            updateData.image_path = null;
        }
        
        console.log('更新产品详情，ID:', id, '显示状态:', updateData[`show`]);
        
        // 更新数据库
        const sql = 'UPDATE products_info SET ? WHERE id = ?';
        db.query(sql, [updateData, id], (err, result) => {
            if (err) {
                console.error('更新产品详情失败:', err);
                res.json({ status: 'error', msg: '更新失败' });
                return;
            }
            
            res.json({ status: 'ok', msg: '更新成功' });
        });
    } catch (e) {
        console.error('更新产品详情失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 添加产品详情
app.post('/apis/add_product_detail/', upload.single('image'), (req, res) => {
    try {
        // 构建插入数据
        const insertData = {
            lang: req.body.lang,
            info_type: req.body.info_type,
            title: req.body.title,
            content: req.body.content,
            url: req.body.url,
            display_order: req.body.display_order || 100,
            // 修复：统一处理show字段，确保是0或1
            show: (req.body.show === 'True' || req.body.show === true || req.body.show === 1 || req.body.show === '1') ? 1 : 0,
            created_at: new Date(),
            update_time: new Date()
        };
        
        // 处理图片
        if (req.file) {
            // 如果上传了图片文件
            insertData.image_path = '/uploads/' + req.file.filename;
        } else if (req.body.image_path === '') {
            // 如果传递了空的image_path，表示不要图片
            insertData.image_path = null;
        }
        
        console.log('添加产品详情，显示状态:', insertData.show);
        
        // 插入数据库
        const sql = 'INSERT INTO products_info SET ?';
        db.query(sql, insertData, (err, result) => {
            if (err) {
                console.error('添加产品详情失败:', err);
                res.json({ status: 'error', msg: '添加失败' });
                return;
            }
            
            res.json({ status: 'ok', msg: '添加成功' });
        });
    } catch (e) {
        console.error('添加产品详情失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 删除产品详情
app.post('/apis/delete_product_detail/', (req, res) => {
    try {
        const id = req.body.id;
        if (!id) {
            res.json({ status: 'error', msg: '缺少产品ID' });
            return;
        }
        
        // 删除数据
        const sql = 'DELETE FROM products_info WHERE id = ?';
        db.query(sql, [id], (err, result) => {
            if (err) {
                console.error('删除产品详情失败:', err);
                res.json({ status: 'error', msg: '删除失败' });
                return;
            }
            
            res.json({ status: 'ok', msg: '删除成功' });
        });
    } catch (e) {
        console.error('删除产品详情失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 切换产品详情显示状态
app.post('/apis/toggle_product_detail_status/', (req, res) => {
    try {
        const id = req.body.id;
        // 修复：统一处理show字段，确保它是数字0或1
        let show = 0;
        if (req.body.show === true || req.body.show === 1 || req.body.show === '1') {
            show = 1;
        }
        
        if (!id) {
            res.json({ status: 'error', msg: '缺少产品ID' });
            return;
        }
        
        console.log(`切换产品详情ID=${id}的显示状态为:`, show);
        
        // 更新显示状态 - 使用反引号转义show关键字
        const sql = 'UPDATE products_info SET `show` = ? WHERE id = ?';
        db.query(sql, [show, id], (err, result) => {
            if (err) {
                console.error('更新显示状态失败:', err);
                res.json({ status: 'error', msg: '更新失败' });
                return;
            }
            
            res.json({ status: 'ok', msg: '更新成功' });
        });
    } catch (e) {
        console.error('更新显示状态失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 添加自动生成产品详情页面API
app.post('/apis/generate_product_pages/', express.json(), (req, res) => {
    try {
        const { productType, productTypeEn } = req.body;
        
        if (!productType || !productTypeEn) {
            return res.json({ 
                status: 'error', 
                msg: '缺少必要参数：中英文产品类型名称' 
            });
        }

        // 直接使用数据库查询获取中文SEO信息
        console.log('查询中文SEO信息:', productType);
        // 修改查询，查找所有相关记录
        db.query(
            'SELECT * FROM products_info WHERE info_type = ? AND lang = 0',
            [productType],
            (err, cnResults) => {
                if (err) {
                    console.error('获取中文SEO信息失败:', err);
                    cnResults = []; // 设置为空数组，避免后续出错
                }
                
                console.log('中文SEO查询结果 (记录数):', cnResults ? cnResults.length : 0);
                console.log('中文SEO查询结果详情:', JSON.stringify(cnResults, null, 2));
                
                // 从中文结果中筛选keywords和description
                let cnKeywords = productType;
                let cnDescription = productType;
                
                if (cnResults && cnResults.length > 0) {
                    // 遍历所有记录，查找标题包含"keywords"的记录
                    for (const record of cnResults) {
                        if (record.title && (record.title.toLowerCase().includes('keywords') || record.title.toLowerCase().includes('关键词'))) {
                            cnKeywords = record.content || productType;
                            console.log('找到中文keywords:', record);
                            break;
                        }
                    }
                    
                    // 遍历所有记录，查找标题包含"description"的记录
                    for (const record of cnResults) {
                        if (record.title && (record.title.toLowerCase().includes('description') || record.title.toLowerCase().includes('描述'))) {
                            cnDescription = record.content || productType;
                            console.log('找到中文description:', record);
                            break;
                        }
                    }
                }
                
                // 直接使用数据库查询获取英文SEO信息
                console.log('查询英文SEO信息:', productTypeEn);
                // 修改查询，查找所有相关记录
                db.query(
                    'SELECT * FROM products_info WHERE info_type = ? AND lang = 1',
                    [productTypeEn],
                    (err, enResults) => {
                        if (err) {
                            console.error('获取英文SEO信息失败:', err);
                            enResults = []; // 设置为空数组，避免后续出错
                        }
                        
                        console.log('英文SEO查询结果 (记录数):', enResults ? enResults.length : 0);
                        console.log('英文SEO查询结果详情:', JSON.stringify(enResults, null, 2));
                        
                        // 从英文结果中筛选keywords和description
                        let enKeywords = productTypeEn;
                        let enDescription = productTypeEn;
                        
                        if (enResults && enResults.length > 0) {
                            // 遍历所有记录，查找标题包含"keywords"的记录
                            for (const record of enResults) {
                                if (record.title && (record.title.toLowerCase().includes('keywords') || record.title.toLowerCase().includes('关键词'))) {
                                    enKeywords = record.content || productTypeEn;
                                    console.log('找到英文keywords:', record);
                                    break;
                                }
                            }
                            
                            // 遍历所有记录，查找标题包含"description"的记录
                            for (const record of enResults) {
                                if (record.title && (record.title.toLowerCase().includes('description') || record.title.toLowerCase().includes('描述'))) {
                                    enDescription = record.content || productTypeEn;
                                    console.log('找到英文description:', record);
                                    break;
                                }
                            }
                        }
                        
                        console.log('提取的SEO信息:', {
                            cnKeywords,
                            cnDescription,
                            enKeywords,
                            enDescription
                        });
        
        // 查询是否有自定义文件名
        db.query(
            `SELECT title, content FROM products_info WHERE info_type = ? AND lang = 0 AND title = 'filename'`,
            [productType],
            (err, cnFilenameResults) => {
                if (err) {
                    return res.json({ 
                        status: 'error', 
                        msg: '获取中文文件名失败: ' + err.message 
                    });
                }
                
                // 查询英文自定义文件名
                db.query(
                    `SELECT title, content FROM products_info WHERE info_type = ? AND lang = 1 AND title = 'filename'`,
                    [productTypeEn],
                    (err, enFilenameResults) => {
                        if (err) {
                            return res.json({ 
                                status: 'error', 
                                msg: '获取英文文件名失败: ' + err.message 
                            });
                        }
                        
                        // 处理文件名
                        let fileNameCN = '';
                        if (cnFilenameResults && cnFilenameResults.length > 0 && cnFilenameResults[0].content) {
                            // 使用数据库中的自定义文件名
                            fileNameCN = cnFilenameResults[0].content;
                            // 确保文件名符合规范
                            fileNameCN = fileNameCN.replace(/[^\w\u4e00-\u9fa5]/g, '');
                        } else {
                            // 默认使用产品类型名称
                            fileNameCN = productType.replace(/[^\w\u4e00-\u9fa5]/g, '');
                        }
                        
                        // 处理英文文件名
                        let fileNameEN = '';
                        if (enFilenameResults && enFilenameResults.length > 0 && enFilenameResults[0].content) {
                            // 使用数据库中的自定义文件名
                            fileNameEN = enFilenameResults[0].content;
                            // 确保文件名符合规范，将空格替换为下划线
                            fileNameEN = fileNameEN.replace(/\s+/g, '_').replace(/[^\w]/g, '');
                        } else {
                            // 默认使用英文产品类型，将空格替换为下划线
                            fileNameEN = productTypeEn.replace(/\s+/g, '_').replace(/[^\w]/g, '');
                        }
                        
                        // 确保英文文件名带有_en后缀
                        if (!fileNameEN.toLowerCase().endsWith('_en')) {
                            fileNameEN += '_en';
                        }
                        
                        // 中文页面模板
                        const cnTemplate = `<!DOCTYPE html>
<html lang="zh-CN" style="height:100%">
<head>
    <meta charset="utf-8">
    <title>厦门贝启科技有限公司-Bearkey-官网</title>
    <meta name="keywords" content="${cnKeywords}">
    <link rel="icon" href="../images/home/<USER>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="description" content="${cnDescription}"/>
    <meta name="author" content=""/>
    <!-- css -->
    <link rel="stylesheet" href="../simple-line-icons/css/simple-line-icons.css">
    <link href="../css/bootstrap.min.css" rel="stylesheet"/>
    <link href="../css/fancybox/jquery.fancybox.css" rel="stylesheet">
    <link href="../css/flexslider.css" rel="stylesheet"/>
    <link href="../css/magnific-popup.css" rel="stylesheet">
    <link href="../css/style.css" rel="stylesheet"/>
    <link href="/css/nav-style.css" rel="stylesheet"/>
    <!-- Font Awesome 图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?0558c2b79797ac39f3317053c376aaa2";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>
<body style="height:100%;overflow-x:hidden;" class="chinese-version">
<div id="wrapper" style="height:100%">

    <!-- start header -->
    <header>
        <!-- 导航栏将通过nav.js动态加载 -->
    </header><!-- end header -->
    <section id="content" class="product_detail_cls" >
        <div>
            <div class="row" style="margin-bottom: 0px;margin-top: -5em;">
            <hr/>
                <div >
            
                
                    <section id="product_info" style="display: block">
                        <div id="product_detail">
                        <!-- 产品详情内容将通过AJAX动态加载 -->
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </section>

</div>
<a href="#" class="scrollup"><i class="fa fa-angle-up active"></i></a>

<!-- javascript
    ================================================== -->
<!-- Placed at the end of the document so the pages load faster -->
<script src="../js/jquery.js"></script>
<script src="../js/jquery.easing.1.3.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script src="../js/jquery.fancybox.pack.js"></script>
<script src="../js/jquery.fancybox-media.js"></script>
<script src="../js/portfolio/jquery.quicksand.js"></script>
<script src="../js/portfolio/setting.js"></script>
<script src="../js/jquery.flexslider.js"></script>
<script src="../js/jquery.isotope.min.js"></script>
<script src="../js/jquery.magnific-popup.min.js"></script>
<script src="../js/animate.js"></script>
<script src="../js/custom.js"></script>
<script src="../js/tools.js"></script>
<script src="../js/nav.js"></script>
<script>window.FOOTER_MANUAL_INIT = true;</script>
<script src="../js/footer.js"></script>
<script src="../js/floating-toolbar.js"></script>
<script src="../js/toolbar-data.js"></script>
<script>
// 获取当前产品类型
var productType = "${productType}";

$(document).ready(function() {
    loadProductDetail();
});

function chg_lang() {
    window.location.href = "${fileNameEN}.html";
}

function loadProductDetail() {
    $.ajax({
        type: "get",
        url: "/apis/product_detail_list/",
        data: { 
            filters: JSON.stringify({
                info_type: productType,
                lang: 0,
                show: 1
            })
        },
        success: function(response) {
            if (response.status === 'ok' && response.data && response.data.length > 0) {
                var productImages = response.data.sort(function(a, b) {
                    return a.display_order - b.display_order;
                });

                console.log('📊 从数据库获取到 ' + response.data.length + ' 条产品详情数据');

                var detailHtml = '';
                var imageCount = 0;
                productImages.forEach(function(item) {
                    if (item.image_path && item.show === 1) {
                        detailHtml += '<p><img src="' + item.image_path + '" alt="' + productType + '" width="100%" /></p>';
                        imageCount++;
                    }
                });

                console.log('🖼️ 需要加载 ' + imageCount + ' 张图片');
                $('#product_detail').html(detailHtml);

                // 等待图片加载完成后再加载页脚
                waitForImagesAndLoadFooter();
            } else {
                console.log('⚠️ 未找到产品详情数据，直接加载页脚');
                $('#product_detail').html('<p>暂无产品详情</p>');
                loadFooter();
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ 加载产品详情失败:', error);
            $('#product_detail').html('<p>加载产品详情失败</p>');
            console.log('⚠️ 加载失败，直接加载页脚');
            loadFooter();
        }
    });
}

function waitForImagesAndLoadFooter() {
    var images = $('#product_detail img');
    var totalImages = images.length;
    var loadedImages = 0;

    console.log('🔄 开始等待 ' + totalImages + ' 张图片加载完成...');

    if (totalImages === 0) {
        console.log('✅ 没有图片需要加载，直接加载页脚');
        loadFooter();
        return;
    }

    images.each(function(index) {
        var img = $(this)[0];

        // 如果图片已经加载完成
        if (img.complete && img.naturalHeight !== 0) {
            loadedImages++;
            console.log('✅ 图片 ' + (index + 1) + '/' + totalImages + ' 已加载完成 (缓存)');
            checkAllImagesLoaded();
        } else {
            // 监听图片加载事件
            $(this).on('load', function() {
                loadedImages++;
                console.log('✅ 图片 ' + (index + 1) + '/' + totalImages + ' 加载完成');
                checkAllImagesLoaded();
            }).on('error', function() {
                loadedImages++;
                console.log('❌ 图片 ' + (index + 1) + '/' + totalImages + ' 加载失败');
                checkAllImagesLoaded();
            });
        }
    });

    function checkAllImagesLoaded() {
        console.log('📈 图片加载进度: ' + loadedImages + '/' + totalImages);
        if (loadedImages === totalImages) {
            console.log('🎉 所有图片加载完成，开始加载页脚');
            loadFooter();
        }
    }
}

function loadFooter() {
    console.log('🔄 开始在最后一张图片后插入页脚...');

    // 找到所有产品详情图片
    var allImages = $('#product_detail img');
    console.log('🖼️ 找到图片数量:', allImages.length);

    // 找到最后一张图片
    var lastImage = allImages.last();
    var insertTarget;

    if (lastImage.length > 0) {
        // 找到最后一张图片的最外层容器
        var imageContainer = lastImage.closest('.col-md-6, .col-sm-6, .col-xs-12, .product-item, div');
        if (imageContainer.length > 0) {
            insertTarget = imageContainer;
            console.log('📍 将在最后一张图片容器后插入页脚');
        } else {
            insertTarget = lastImage;
            console.log('📍 将在最后一张图片元素后插入页脚');
        }
    } else {
        // 如果没有图片，插入到产品详情区域末尾
        insertTarget = $('#product_detail');
        console.log('📍 没有找到图片，将在产品详情区域末尾插入页脚');
    }

    // 如果页脚已存在，先移除
    if ($('footer').length > 0) {
        console.log('🗑️ 移除现有页脚');
        $('footer').remove();
    }

    // 在正确位置插入页脚
    insertTarget.after('<footer></footer>');
    console.log('📌 页脚已插入到正确位置');

    var showFooter = function() {
        if (typeof window.initFooter === 'function') {
            console.log('📝 开始初始化页脚内容...');
            window.initFooter();
            console.log('✨ 页脚内容加载完成');
        }
    };

    if (!window.initFooter) {
        console.log('📦 开始加载页脚脚本...');
        var script = document.createElement('script');
        script.src = '../js/footer.js';
        script.onload = showFooter;
        document.body.appendChild(script);
    } else {
        showFooter();
    }
}
</script>
</body>
</html>`;

                        // 英文页面模板
                        const enTemplate = `<!DOCTYPE html>
<html lang="en" style="height:100%">
<head>
    <meta charset="utf-8">
    <title>Xiamen Bearkey Technology Co., Ltd. - Official Website</title>
    <meta name="keywords" content="${enKeywords}">
    <link rel="icon" href="../images/home/<USER>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="description" content="${enDescription}"/>
    <meta name="author" content=""/>
    <!-- css -->
    <link rel="stylesheet" href="../simple-line-icons/css/simple-line-icons.css">
    <link href="../css/bootstrap.min.css" rel="stylesheet"/>
    <link href="../css/fancybox/jquery.fancybox.css" rel="stylesheet">
    <link href="../css/flexslider.css" rel="stylesheet"/>
    <link href="../css/magnific-popup.css" rel="stylesheet">
    <link href="../css/style.css" rel="stylesheet"/>
    <link href="../css/gallery-1.css" rel="stylesheet">
    <link href="/css/nav-style.css" rel="stylesheet"/>
    <!-- Font Awesome icon library -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?0558c2b79797ac39f3317053c376aaa2";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>
<body style="height:100%;overflow-x:hidden;" class="chinese-version">
<div id="wrapper" style="height:100%">

    <!-- start header -->
    <header>
        <!-- Navigation will be loaded dynamically by nav.js -->
    </header><!-- end header -->
    
    <section id="content" class="product_detail_cls" >
        <div>
            <div class="row" style="margin-bottom: 0px;margin-top: -5em;">
            <hr/>
                <div >
            
                
                    <section id="product_info" style="display: block">
                        <div id="product_detail">
                        <!-- Product details will be loaded dynamically via AJAX -->
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </section>
</div>
<a href="#" class="scrollup"><i class="fa fa-angle-up active"></i></a>

<!-- javascript
    ================================================== -->
<!-- Placed at the end of the document so the pages load faster -->
<script src="../js/jquery.js"></script>
<script src="../js/jquery.easing.1.3.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script src="../js/jquery.fancybox.pack.js"></script>
<script src="../js/jquery.fancybox-media.js"></script>
<script src="../js/portfolio/jquery.quicksand.js"></script>
<script src="../js/portfolio/setting.js"></script>
<script src="../js/jquery.flexslider.js"></script>
<script src="../js/jquery.isotope.min.js"></script>
<script src="../js/jquery.magnific-popup.min.js"></script>
<script src="../js/animate.js"></script>
<script src="../js/custom.js"></script>
<script src="../js/tools.js"></script>
<script src="../js/nav.js"></script>
<script src="../js/footer.js"></script>
<script src="../js/floating-toolbar.js"></script>
<script src="../js/toolbar-data.js"></script>
<script>
// 英文产品类型名称
var productTypeEn = "${productTypeEn}";

// Execute when page is loaded
$(document).ready(function() {
    // Load product details
    loadProductDetail();
});

// Custom language switch function
function chg_lang() {
    // Switch to Chinese version
    window.location.href = "${fileNameCN}.html";
}

// Load product details
function loadProductDetail() {
    $.ajax({
        type: "get",
        url: "/apis/product_detail_list/",
        data: {
            filters: JSON.stringify({
                info_type: productTypeEn,  // 使用英文产品类型作为筛选条件
                lang: 1,  // 英文语言筛选
                show: 1   // 只显示开启显示的内容
            })
        },
        success: function(response) {
            if (response.status === 'ok' && response.data && response.data.length > 0) {
                console.log('📊 Retrieved ' + response.data.length + ' product detail records from database');

                // Sort by display order (ascending)
                var productImages = response.data.sort(function(a, b) {
                    return a.display_order - b.display_order;
                });

                // Generate product details HTML
                var detailHtml = '';
                productImages.forEach(function(item) {
                    // Check show status, only display items with show set to 1
                    if (item.image_path && item.show === 1) {
                        detailHtml += '<p><img src="' + item.image_path + '" alt="' + productTypeEn + '" width="100%" /></p>';
                    }
                });

                // Update product details content
                $('#product_detail').html(detailHtml);

                // Start waiting for images to load
                waitForImagesLoad();
            } else {
                console.error('No product details found');
                $('#product_detail').html('<p>No product details available</p>');
                // Load footer immediately if no images
                loadFooter();
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to load product details:', error);
            $('#product_detail').html('<p>Failed to load product details</p>');
            // Load footer immediately on error
            loadFooter();
        }
    });
}

// Wait for all images to load
function waitForImagesLoad() {
    var images = $('#product_detail img');
    var totalImages = images.length;
    var loadedImages = 0;

    console.log('🖼️ Need to load ' + totalImages + ' images');

    if (totalImages === 0) {
        console.log('✅ No images to load, loading footer directly');
        loadFooter();
        return;
    }

    console.log('🔄 Starting to wait for ' + totalImages + ' images to complete loading...');

    images.each(function(index) {
        var img = $(this)[0];

        // If image is already loaded
        if (img.complete && img.naturalHeight !== 0) {
            loadedImages++;
            console.log('✅ Image ' + (index + 1) + '/' + totalImages + ' already loaded (cached)');
            checkAllImagesLoaded();
        } else {
            // Listen for image load events
            $(this).on('load', function() {
                loadedImages++;
                console.log('✅ Image ' + (index + 1) + '/' + totalImages + ' loaded');
                checkAllImagesLoaded();
            }).on('error', function() {
                loadedImages++;
                console.log('❌ Image ' + (index + 1) + '/' + totalImages + ' failed to load');
                checkAllImagesLoaded();
            });
        }
    });

    function checkAllImagesLoaded() {
        console.log('📈 Image loading progress: ' + loadedImages + '/' + totalImages);
        if (loadedImages === totalImages) {
            console.log('🎉 All images loaded, adding images-loaded class');
            // Add images-loaded class to allow footer display
            document.body.classList.add('images-loaded');
            console.log('🔄 Starting to load footer');
            loadFooter();
        }
    }
}

function loadFooter() {
    console.log('🔄 Starting to insert footer after last image...');

    // Find all product detail images
    var allImages = $('#product_detail img');
    console.log('🖼️ Found image count:', allImages.length);

    // Find the last image
    var lastImage = allImages.last();
    var insertTarget;

    if (lastImage.length > 0) {
        // Find the outermost container of the last image
        var imageContainer = lastImage.closest('.col-md-6, .col-sm-6, .col-xs-12, .product-item, div');
        if (imageContainer.length > 0) {
            insertTarget = imageContainer;
            console.log('📍 Will insert footer after last image container');
        } else {
            insertTarget = lastImage;
            console.log('📍 Will insert footer after last image element');
        }
    } else {
        // If no images, insert at end of product detail area
        insertTarget = $('#product_detail');
        console.log('📍 No images found, will insert footer at end of product detail area');
    }

    // Remove existing footer if present
    if ($('footer').length > 0) {
        console.log('🗑️ Removing existing footer');
        $('footer').remove();
    }

    // Insert footer at correct position
    insertTarget.after('<footer></footer>');
    console.log('📌 Footer inserted at correct position');

    var showFooter = function() {
        if (typeof window.initFooter === 'function') {
            console.log('📝 Starting to initialize footer content...');
            window.initFooter();
            console.log('✨ Footer content loaded');
        }
    };

    if (!window.initFooter) {
        console.log('📦 Starting to load footer script...');
        var script = document.createElement('script');
        script.src = '../js/footer.js';
        script.onload = showFooter;
        document.body.appendChild(script);
    } else {
        showFooter();
    }
}
</script>
</body>
</html>`;

                        // 确保目录存在
                        const productDir = path.join(__dirname, 'public', 'product');
                        if (!fs.existsSync(productDir)) {
                            fs.mkdirSync(productDir, { recursive: true });
                        }
                                        
                                        // 打印模板中的SEO信息
                                        console.log('中文模板SEO信息:', {
                                            keywords: `<meta name="keywords" content="${cnKeywords}">`,
                                            description: `<meta name="description" content="${cnDescription}"/>`
                                        });
                                        
                                        console.log('英文模板SEO信息:', {
                                            keywords: `<meta name="keywords" content="${enKeywords}">`,
                                            description: `<meta name="description" content="${enDescription}"/>`
                                        });
                        
                        // 写入中文页面
                        const cnFilePath = path.join(productDir, `${fileNameCN}.html`);
                        fs.writeFileSync(cnFilePath, cnTemplate, 'utf8');
                        
                        // 写入英文页面
                        const enFilePath = path.join(productDir, `${fileNameEN}.html`);
                        fs.writeFileSync(enFilePath, enTemplate, 'utf8');
                        
                        // 返回成功信息和文件路径
                        res.json({
                            status: 'ok',
                            msg: '页面生成成功',
                            files: {
                                chinese: `/product/${fileNameCN}.html`,
                                english: `/product/${fileNameEN}.html`
                            }
                        });
                                    }
                                );
                            }
                        );
                    }
                );
            }
        );
    } catch (error) {
        res.json({ 
            status: 'error', 
            msg: '生成页面失败: ' + error.message 
        });
    }
});

/**
 * 格式化文件名，确保符合Web命名规范
 * 处理空格、特殊字符等
 * @param {string} filename 原始文件名
 * @returns {string} 格式化后的文件名
 */
function formatFilename(filename) {
    let formattedName = filename;
    
    // 移除文件扩展名
    const extension = formattedName.includes('.') ? formattedName.substring(formattedName.lastIndexOf('.')) : '';
    formattedName = formattedName.replace(extension, '');
    
    // 将空格替换为下划线
    formattedName = formattedName.replace(/\s+/g, '_');
    
    // 移除非法字符（只保留字母、数字、下划线、中文字符）
    formattedName = formattedName.replace(/[^\w\u4e00-\u9fa5]/g, '');
    
    // 处理英文页面文件名，确保包含_en后缀
    if (filename.toLowerCase().includes('_en') || 
        (formattedName.match(/^[a-zA-Z0-9_]+$/) && !formattedName.includes('_en'))) {
        // 如果文件名全是英文且没有_en后缀，或者原本就包含_en，添加_en后缀
        if (!formattedName.toLowerCase().endsWith('_en')) {
            formattedName += '_en';
        }
    }
    
    // 重新添加文件扩展名
    return formattedName + extension;
}

// ====== 资料下载导航API ======
// 获取资料下载导航列表
app.get('/apis/download_nav_list/', (req, res) => {
    try {
        // 获取分页参数
        const page = parseInt(req.query.page) || 1;
        const size = parseInt(req.query.size) || 10;
        const offset = (page - 1) * size;
        
        // 构建查询条件
        let whereClause = '1=1';
        let params = [];
        
        // 处理filters参数
        if (req.query.lang !== undefined && req.query.lang !== '') {
            whereClause += ' AND lang = ?';
            params.push(req.query.lang);
        }
        
        if (req.query.type !== undefined && req.query.type !== '') {
            whereClause += ' AND type = ?';
            params.push(req.query.type);
        }
        
        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM download_nav WHERE ${whereClause}`;
        db.query(countSql, params, (err, countResult) => {
            if (err) {
                console.error('查询导航总数失败:', err);
                return res.json({ status: 'error', msg: '查询失败' });
            }
            
            const total = countResult[0].total;
            
            // 查询分页数据
            const sql = `
                SELECT * FROM download_nav 
                WHERE ${whereClause}
                ORDER BY lang ASC, display_order ASC, id DESC 
                LIMIT ? OFFSET ?
            `;
            
            db.query(sql, [...params, size, offset], (err, results) => {
                if (err) {
                    console.error('查询导航列表失败:', err);
                    return res.json({ status: 'error', msg: '查询失败' });
                }
                
                res.json({
                    status: 'ok',
                    data: results,
                    total: total
                });
            });
        });
    } catch (e) {
        console.error('获取导航列表失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 获取单个导航详情
app.get('/apis/download_nav_detail/', (req, res) => {
    try {
        const id = req.query.id;
        if (!id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        
        const sql = 'SELECT * FROM download_nav WHERE id = ?';
        db.query(sql, [id], (err, results) => {
            if (err) {
                console.error('查询导航详情失败:', err);
                return res.json({ status: 'error', msg: '查询失败' });
            }
            
            if (results.length === 0) {
                return res.json({ status: 'error', msg: '未找到对应的导航' });
            }
            
            res.json({
                status: 'ok',
                data: results[0]
            });
        });
    } catch (e) {
        console.error('获取导航详情失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 添加导航
app.post('/apis/add_download_nav/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { type, title, content, display_order, lang, show, url, image_path, update_time } = req.body;
        
        if (!title) {
            return res.json({ status: 'error', msg: '标题不能为空' });
        }
        
        // 修改SQL语句,添加update_time字段
        const sql = 'INSERT INTO download_nav (type, title, content, display_order, lang, `show`, url, image_path, update_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)';
        db.query(sql, [type, title, content, display_order, lang, show, url, image_path, update_time || new Date()], (err, result) => {
            if (err) {
                console.error('添加导航失败:', err);
                return res.json({ status: 'error', msg: '添加失败' });
            }
            
            res.json({
                status: 'ok',
                msg: '添加成功',
                id: result.insertId
            });
        });
    } catch (e) {
        console.error('添加导航失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 更新导航
app.post('/apis/update_download_nav/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { id, type, title, content, display_order, lang, show, url, image_path, update_time } = req.body;
        
        if (!id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        
        if (!title) {
            return res.json({ status: 'error', msg: '标题不能为空' });
        }
        
        let sql, params;
        if (update_time) {
            // 如果前端提供了update_time,使用它
            sql = 'UPDATE download_nav SET type = ?, title = ?, content = ?, display_order = ?, lang = ?, `show` = ?, url = ?, image_path = ?, update_time = ? WHERE id = ?';
            params = [type, title, content, display_order, lang, show, url, image_path, update_time, id];
        } else {
            // 否则让数据库自动更新(ON UPDATE CURRENT_TIMESTAMP)
            sql = 'UPDATE download_nav SET type = ?, title = ?, content = ?, display_order = ?, lang = ?, `show` = ?, url = ?, image_path = ? WHERE id = ?';
            params = [type, title, content, display_order, lang, show, url, image_path, id];
        }
        
        db.query(sql, params, (err, result) => {
            if (err) {
                console.error('更新导航失败:', err);
                return res.json({ status: 'error', msg: '更新失败' });
            }
            
            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到要更新的导航' });
            }
            
            res.json({
                status: 'ok',
                msg: '更新成功'
            });
        });
    } catch (e) {
        console.error('更新导航失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 删除导航
app.post('/apis/delete_download_nav/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { id } = req.body;
        
        if (!id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        
        const sql = 'DELETE FROM download_nav WHERE id = ?';
        db.query(sql, [id], (err, result) => {
            if (err) {
                console.error('删除导航失败:', err);
                return res.json({ status: 'error', msg: '删除失败' });
            }
            
            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到要删除的导航' });
            }
            
            res.json({
                status: 'ok',
                msg: '删除成功'
            });
        });
    } catch (e) {
        console.error('删除导航失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 切换导航显示状态
app.post('/apis/toggle_download_nav_show/', express.json(), (req, res) => {
    try {
        const { id, show } = req.body;
        
        if (!id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        
        const sql = 'UPDATE download_nav SET `show` = ? WHERE id = ?';
        db.query(sql, [show ? 1 : 0, id], (err, result) => {
            if (err) {
                console.error('更新导航显示状态失败:', err);
                return res.json({ status: 'error', msg: '更新失败' });
            }
            
            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到要更新的导航' });
            }
            
            res.json({
                status: 'ok',
                msg: '更新成功'
            });
        });
    } catch (e) {
        console.error('更新导航显示状态失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// ====== 服务与支持API ======
// 获取服务与支持列表
app.get('/apis/support_list/', (req, res) => {
    try {
        // 获取分页参数
        const page = parseInt(req.query.page) || 1;
        const size = parseInt(req.query.size) || 10;
        const offset = (page - 1) * size;

        // 构建查询条件
        let whereClause = '1=1';
        let params = [];

        // 处理filters参数
        if (req.query.lang !== undefined && req.query.lang !== '') {
            whereClause += ' AND lang = ?';
            params.push(req.query.lang);
        }

        if (req.query.type !== undefined && req.query.type !== '') {
            whereClause += ' AND type = ?';
            params.push(req.query.type);
        }

        if (req.query.source_id !== undefined && req.query.source_id !== '') {
            whereClause += ' AND source_id = ?';
            params.push(req.query.source_id);
        }

        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM support WHERE ${whereClause}`;
        db.query(countSql, params, (err, countResult) => {
            if (err) {
                console.error('查询服务与支持总数失败:', err);
                return res.json({ status: 'error', msg: '查询失败' });
            }

            const total = countResult[0].total;

            // 查询分页数据
            const sql = `
                SELECT * FROM support
                WHERE ${whereClause}
                ORDER BY lang ASC, display_order ASC, id DESC
                LIMIT ? OFFSET ?
            `;

            db.query(sql, [...params, size, offset], (err, results) => {
                if (err) {
                    console.error('查询服务与支持列表失败:', err);
                    return res.json({ status: 'error', msg: '查询失败' });
                }

                res.json({
                    status: 'ok',
                    data: results,
                    total: total
                });
            });
        });
    } catch (e) {
        console.error('获取服务与支持列表失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 获取单个服务与支持详情
app.get('/apis/support_detail/', (req, res) => {
    try {
        const id = req.query.id;
        if (!id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }

        const sql = 'SELECT * FROM support WHERE id = ?';
        db.query(sql, [id], (err, results) => {
            if (err) {
                console.error('查询服务与支持详情失败:', err);
                return res.json({ status: 'error', msg: '查询失败' });
            }

            if (results.length === 0) {
                return res.json({ status: 'error', msg: '未找到对应的服务与支持' });
            }

            res.json({
                status: 'ok',
                data: results[0]
            });
        });
    } catch (e) {
        console.error('获取服务与支持详情失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 添加服务与支持
app.post('/apis/add_support/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { type, title, content, html_content, display_order, lang, show, url, image_path, update_time, source_id } = req.body;

        console.log('=== 添加支持内容请求 ===');
        console.log('接收到的数据:', {
            type, title, content: content ? content.substring(0, 50) + '...' : '空',
            html_content: html_content ? html_content.substring(0, 100) + '...' : '空',
            html_content_length: html_content ? html_content.length : 0,
            display_order, lang, show, url, image_path, source_id
        });

        if (!title) {
            return res.json({ status: 'error', msg: '标题不能为空' });
        }

        const sql = 'INSERT INTO support (type, title, content, html_content, display_order, lang, `show`, url, image_path, update_time, source_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)';
        const params = [type, title, content, html_content, display_order, lang, show, url, image_path, update_time || new Date(), source_id];

        console.log('执行SQL:', sql);
        console.log('SQL参数:', params.map((p, i) => i === 3 ? (p ? `html_content(${p.length}字符)` : 'null') : p));

        db.query(sql, params, (err, result) => {
            if (err) {
                console.error('添加服务与支持失败:', err);
                return res.json({ status: 'error', msg: '添加失败' });
            }

            console.log('添加成功，新记录ID:', result.insertId);
            res.json({
                status: 'ok',
                msg: '添加成功',
                id: result.insertId
            });
        });
    } catch (e) {
        console.error('添加服务与支持失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 更新服务与支持
app.post('/apis/update_support/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { id, type, title, content, html_content, display_order, lang, show, url, image_path, update_time } = req.body;

        if (!id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }

        // 如果有html_content，则不需要验证title（参考微信文章编辑器）
        if (!html_content && !title) {
            return res.json({ status: 'error', msg: '标题不能为空' });
        }

        console.log('更新支持内容请求:', {
            id,
            type,
            title,
            content_length: content ? content.length : 0,
            html_content_length: html_content ? html_content.length : 0,
            show
        });

        let sql, params;
        if (update_time) {
            sql = 'UPDATE support SET type = ?, title = ?, content = ?, html_content = ?, display_order = ?, lang = ?, `show` = ?, url = ?, image_path = ?, update_time = ? WHERE id = ?';
            params = [type, title, content, html_content, display_order, lang, show, url, image_path, update_time, id];
        } else {
            sql = 'UPDATE support SET type = ?, title = ?, content = ?, html_content = ?, display_order = ?, lang = ?, `show` = ?, url = ?, image_path = ? WHERE id = ?';
            params = [type, title, content, html_content, display_order, lang, show, url, image_path, id];
        }

        db.query(sql, params, (err, result) => {
            if (err) {
                console.error('更新服务与支持失败:', err);
                return res.json({ status: 'error', msg: '更新失败' });
            }

            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到要更新的服务与支持' });
            }

            console.log('支持内容更新成功，ID:', id);

            res.json({
                status: 'ok',
                msg: '更新成功'
            });
        });
    } catch (e) {
        console.error('更新服务与支持失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 删除服务与支持
app.post('/apis/delete_support/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const id = req.body.id;
        if (!id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }

        const sql = 'DELETE FROM support WHERE id = ?';
        db.query(sql, [id], (err, result) => {
            if (err) {
                console.error('删除服务与支持失败:', err);
                return res.json({ status: 'error', msg: '删除失败' });
            }

            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到要删除的服务与支持' });
            }

            res.json({
                status: 'ok',
                msg: '删除成功'
            });
        });
    } catch (e) {
        console.error('删除服务与支持失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 切换服务与支持显示状态
app.post('/apis/toggle_support_show/', express.json(), (req, res) => {
    try {
        const { id, show } = req.body;

        if (!id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }

        const sql = 'UPDATE support SET `show` = ? WHERE id = ?';
        db.query(sql, [show ? 1 : 0, id], (err, result) => {
            if (err) {
                console.error('更新服务与支持显示状态失败:', err);
                return res.json({ status: 'error', msg: '更新失败' });
            }

            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到要更新的服务与支持' });
            }

            res.json({
                status: 'ok',
                msg: '更新成功'
            });
        });
    } catch (e) {
        console.error('更新服务与支持显示状态失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 通过标题获取服务与支持（参考微信文章编辑器）
app.post('/apis/get_support_by_title/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { title, type, lang } = req.body;

        console.log('通过标题获取支持内容请求:', { title, type, lang });

        if (!title) {
            return res.json({ status: 'error', msg: '缺少标题参数' });
        }

        // 构建查询条件
        let whereClause = 'title = ?';
        let params = [title];

        if (type) {
            whereClause += ' AND type = ?';
            params.push(type);
        }

        if (lang !== undefined && lang !== '') {
            whereClause += ' AND lang = ?';
            params.push(lang);
        }

        const sql = `SELECT * FROM support WHERE ${whereClause} ORDER BY id DESC`;

        db.query(sql, params, (err, results) => {
            if (err) {
                console.error('通过标题查询服务与支持失败:', err);
                return res.json({ status: 'error', msg: '查询失败' });
            }

            console.log('查询结果:', results.length, '条记录');

            // 如果有html_content，优先使用html_content；否则构建包含标题的内容
            const processedResults = results.map(item => {
                if (item.html_content && item.html_content.trim()) {
                    // 如果有保存的HTML内容，直接使用
                    return {
                        ...item,
                        html_content: item.html_content
                    };
                } else {
                    // 如果没有HTML内容，构建包含标题的HTML内容（参考微信文章编辑器）
                    let htmlContent = '';
                    if (item.title && item.title.trim()) {
                        htmlContent += `<h2>${item.title}</h2>`;
                    }
                    if (item.content && item.content.trim()) {
                        htmlContent += item.content;
                    }
                    return {
                        ...item,
                        html_content: htmlContent
                    };
                }
            });

            res.json({
                status: 'ok',
                data: processedResults
            });
        });
    } catch (e) {
        console.error('通过标题获取服务与支持失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 通过ID更新支持内容的HTML内容（参考微信文章编辑器）
app.post('/apis/update_support_html/', express.urlencoded({ extended: true }), (req, res) => {
    try {
        const { id, title, content, html_content, show } = req.body;

        console.log('更新支持内容HTML请求:', {
            id,
            title,
            content_length: content ? content.length : 0,
            html_content_length: html_content ? html_content.length : 0,
            show
        });

        if (!id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }

        if (!html_content || !html_content.trim()) {
            return res.json({ status: 'error', msg: '内容不能为空' });
        }

        // 只更新必要的字段，保持其他字段不变
        const sql = 'UPDATE support SET title = ?, content = ?, html_content = ?, `show` = ? WHERE id = ?';
        const params = [title, content, html_content, show ? 1 : 0, id];

        db.query(sql, params, (err, result) => {
            if (err) {
                console.error('更新支持内容HTML失败:', err);
                return res.json({ status: 'error', msg: '更新失败' });
            }

            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到要更新的支持内容' });
            }

            console.log('支持内容HTML更新成功，ID:', id);

            res.json({
                status: 'ok',
                msg: '更新成功'
            });
        });
    } catch (e) {
        console.error('更新支持内容HTML失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// ====== 资料下载详情API ======
    // 获取资料下载详情列表
    app.get('/apis/download_detail_list/', (req, res) => {
        try {
        // 获取分页参数
            const page = parseInt(req.query.page) || 1;
            const size = parseInt(req.query.size) || 10;
            const offset = (page - 1) * size;
        
        // 构建查询条件
            let whereClause = '1=1';
            let params = [];
        
        // 处理filters参数
        if (req.query.filters) {
            try {
                const filters = JSON.parse(req.query.filters);
                
                if (filters.lang !== undefined && filters.lang !== '') {
                    whereClause += ' AND lang = ?';
                    params.push(filters.lang);
                }
                
                if (filters.type !== undefined && filters.type !== '') {
                    whereClause += ' AND type = ?';
                    params.push(filters.type);
                }
                
                if (filters.show !== undefined) {
                    whereClause += ' AND `show` = ?';
                    params.push(filters.show ? 1 : 0);
                }
            } catch (e) {
                console.error('解析filters参数失败:', e);
            }
        } else {
            // 兼容单独参数
            if (req.query.lang !== undefined && req.query.lang !== '') {
                whereClause += ' AND lang = ?';
                params.push(req.query.lang);
            }
            
            if (req.query.type !== undefined && req.query.type !== '') {
                whereClause += ' AND type = ?';
                params.push(req.query.type);
            }
            }
        
        // 查询总数
            const countSql = `SELECT COUNT(*) as total FROM download_detail WHERE ${whereClause}`;
            db.query(countSql, params, (err, countResult) => {
                if (err) {
                console.error('查询下载详情总数失败:', err);
                return res.json({ status: 'error', msg: '查询失败' });
                }
            
                const total = countResult[0].total;
            
            // 查询分页数据
            const sql = `
                SELECT * FROM download_detail 
                WHERE ${whereClause}
                ORDER BY lang ASC, display_order ASC, id DESC 
                LIMIT ? OFFSET ?
            `;
            
            db.query(sql, [...params, size, offset], (err, results) => {
                    if (err) {
                    console.error('查询下载详情列表失败:', err);
                    return res.json({ status: 'error', msg: '查询失败' });
                    }
                
                res.json({
                    status: 'ok',
                    data: results,
                    total: total
                });
                });
            });
        } catch (e) {
        console.error('获取下载详情列表失败:', e);
            res.json({ status: 'error', msg: '服务器错误' });
        }
    });

// 获取所有资料下载详情类型（去重）
    app.get('/apis/download_detail_types/', (req, res) => {
            db.query('SELECT DISTINCT type FROM download_detail WHERE type IS NOT NULL AND type != ""', (err, results) => {
                if (err) return res.json({ status: 'error', msg: '查询失败' });
                const types = results.map(r => r.type);
                res.json({ status: 'ok', data: types });
            });
    });

// 获取单个资料下载详情
    app.get('/apis/download_detail_detail/', (req, res) => {
        try {
            const id = req.query.id;
        if (!id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        
        const sql = 'SELECT * FROM download_detail WHERE id = ?';
        db.query(sql, [id], (err, results) => {
            if (err) {
                console.error('查询下载详情失败:', err);
                return res.json({ status: 'error', msg: '查询失败' });
            }
            
            if (results.length === 0) {
                return res.json({ status: 'error', msg: '未找到对应的下载详情' });
            }
            
            res.json({
                status: 'ok',
                data: results[0]
            });
            });
        } catch (e) {
        console.error('获取下载详情失败:', e);
            res.json({ status: 'error', msg: '服务器错误' });
        }
    });

// 添加资料下载详情
app.post('/apis/add_download_detail/', express.json(), (req, res) => {
        try {
            const { type, title, content, display_order, lang, show, url, image_path } = req.body;
        
        if (!type) {
            return res.json({ status: 'error', msg: '分类类型不能为空' });
        }
        
        if (!title) {
            return res.json({ status: 'error', msg: '标题不能为空' });
        }
        
        const sql = 'INSERT INTO download_detail (type, title, content, display_order, lang, `show`, url, image_path) VALUES (?, ?, ?, ?, ?, ?, ?, ?)';
        db.query(sql, [type, title, content, display_order, lang, show, url, image_path], (err, result) => {
                if (err) {
                console.error('添加下载详情失败:', err);
                return res.json({ status: 'error', msg: '添加失败' });
                }
            
            res.json({
                status: 'ok',
                msg: '添加成功',
                id: result.insertId
            });
            });
        } catch (e) {
        console.error('添加下载详情失败:', e);
            res.json({ status: 'error', msg: '服务器错误' });
        }
    });

// 更新资料下载详情
app.post('/apis/update_download_detail/', express.json(), (req, res) => {
        try {
            const { id, type, title, content, display_order, lang, show, url, image_path } = req.body;
        
        if (!id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        
        if (!type) {
            return res.json({ status: 'error', msg: '分类类型不能为空' });
        }
        
        if (!title) {
            return res.json({ status: 'error', msg: '标题不能为空' });
        }
        
        const sql = 'UPDATE download_detail SET type = ?, title = ?, content = ?, display_order = ?, lang = ?, `show` = ?, url = ?, image_path = ? WHERE id = ?';
        db.query(sql, [type, title, content, display_order, lang, show, url, image_path, id], (err, result) => {
            if (err) {
                console.error('更新下载详情失败:', err);
                return res.json({ status: 'error', msg: '更新失败' });
            }
            
            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到要更新的下载详情' });
            }
            
            res.json({
                status: 'ok',
                msg: '更新成功'
            });
            });
        } catch (e) {
        console.error('更新下载详情失败:', e);
            res.json({ status: 'error', msg: '服务器错误' });
        }
    });

// 删除资料下载详情
app.post('/apis/delete_download_detail/', express.json(), (req, res) => {
        try {
            const { id } = req.body;
        
        if (!id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        
        const sql = 'DELETE FROM download_detail WHERE id = ?';
        db.query(sql, [id], (err, result) => {
            if (err) {
                console.error('删除下载详情失败:', err);
                return res.json({ status: 'error', msg: '删除失败' });
            }
            
            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到要删除的下载详情' });
            }
            
            res.json({
                status: 'ok',
                msg: '删除成功'
            });
            });
        } catch (e) {
        console.error('删除下载详情失败:', e);
            res.json({ status: 'error', msg: '服务器错误' });
        }
    });

// 切换资料下载详情显示状态
app.post('/apis/toggle_download_detail_show/', express.json(), (req, res) => {
        try {
            const { id, show } = req.body;
        
        if (!id) {
            return res.json({ status: 'error', msg: '缺少ID参数' });
        }
        
        const sql = 'UPDATE download_detail SET `show` = ? WHERE id = ?';
        db.query(sql, [show ? 1 : 0, id], (err, result) => {
            if (err) {
                console.error('更新下载详情显示状态失败:', err);
                return res.json({ status: 'error', msg: '更新失败' });
        }
            
            if (result.affectedRows === 0) {
                return res.json({ status: 'error', msg: '未找到要更新的下载详情' });
            }
            
            res.json({
                status: 'ok',
                msg: '更新成功'
            });
        });
    } catch (e) {
        console.error('更新下载详情显示状态失败:', e);
        res.json({ status: 'error', msg: '服务器错误' });
    }
});

// 添加自动生成下载页面API
app.post('/apis/generate_download_pages/', express.json(), (req, res) => {
    try {
        const { productName, productNameZh, productNameEn } = req.body;
        const zhName = productNameZh || productName || '';
        const enName = productNameEn || productName || '';

        if (!zhName || !enName) {
            return res.json({
                status: 'error',
                msg: '缺少必要参数：中英文产品名称'
            });
        }

        // 生成文件名
        const fileNameCN = zhName.replace(/[^\w\u4e00-\u9fa5]/g, '');
        // 直接使用输入的英文产品名生成文件名
        const fileNameEN = enName + '_en';

        // 读取模板
        const cnTemplatePath = path.join(__dirname, 'public', 'download', '8英寸平板.html');
        const enTemplatePath = path.join(__dirname, 'public', 'download', '8-inch tablet_en.html');

        if (!fs.existsSync(cnTemplatePath) || !fs.existsSync(enTemplatePath)) {
            return res.json({
                status: 'error',
                msg: '模板文件不存在'
            });
        }

        // 读取并替换内容
        let cnContent = fs.readFileSync(cnTemplatePath, 'utf8').replace(/8英寸平板/g, zhName);
        let enContent = fs.readFileSync(enTemplatePath, 'utf8').replace(/8[- ]inch tablet/gi, enName);

        // 写入新文件
        const cnTargetPath = path.join(__dirname, 'public', 'download', `${fileNameCN}.html`);
        const enTargetPath = path.join(__dirname, 'public', 'download', `${fileNameEN}.html`);
        fs.writeFileSync(cnTargetPath, cnContent);
        fs.writeFileSync(enTargetPath, enContent);

        res.json({
            status: 'ok',
            files: {
                chinese: `${fileNameCN}.html`,
                english: `${fileNameEN}.html`
            }
        });
    } catch (e) {
        res.json({ status: 'error', msg: '生成失败: ' + e.message });
    }
});

// 添加商业定制提交接口
app.post('/apis/business_customization/submit', async (req, res) => {
    console.log('收到商业定制表单提交请求:', req.body);
    
    if (!req.body.name || !req.body.phone || !req.body.email || !req.body.company || !req.body.custom_type || !req.body.requirements) {
        console.error('表单数据不完整:', req.body);
        return res.json({
            status: 'error',
            message: '请填写所有必填字段'
        });
    }

    const insertSQL = `
        INSERT INTO business_customization 
        (name, phone, email, company, custom_type, requirements)
        VALUES (?, ?, ?, ?, ?, ?)
    `;

    try {
        console.log('开始保存商业定制信息到数据库');
        await db.promise().query(insertSQL, [
            req.body.name,
            req.body.phone,
            req.body.email,
            req.body.company,
            req.body.custom_type,
            req.body.requirements
        ]);
        
        console.log('商业定制信息保存成功');
        res.json({
            status: 'ok',
            message: '提交成功'
        });
    } catch (error) {
        console.error('保存商业定制信息失败，详细错误:', error);
        console.error('错误堆栈:', error.stack);
        res.json({
            status: 'error',
            message: '提交失败，请稍后重试'
        });
    }
});
// 获取方案定制列表
app.get('/apis/business_customization/list', (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 10;
    const offset = (page - 1) * size;
    
    // 获取总记录数
    const countSQL = 'SELECT COUNT(*) as total FROM business_customization';
    
    // 获取分页数据
    const listSQL = `
        SELECT * FROM business_customization 
        ORDER BY submit_time DESC 
        LIMIT ? OFFSET ?
    `;
    
    db.query(countSQL, (err, countResult) => {
        if (err) {
            console.error('获取方案定制总数失败:', err);
            return res.json({
                status: 'error',
                message: '获取数据失败'
            });
        }
        
        const total = countResult[0].total;
        
        db.query(listSQL, [size, offset], (err, results) => {
            if (err) {
                console.error('获取方案定制列表失败:', err);
                return res.json({
                    status: 'error',
                    message: '获取数据失败'
                });
            }
            
            res.json({
                status: 'ok',
                data: results,
                total: total
            });
        });
    });
});

// 获取方案定制详情
app.get('/apis/business_customization/detail', (req, res) => {
    const id = parseInt(req.query.id);
    
    if (!id) {
        return res.json({
            status: 'error',
            message: '参数错误'
        });
    }
    
    const sql = 'SELECT * FROM business_customization WHERE id = ?';
    
    db.query(sql, [id], (err, results) => {
        if (err) {
            console.error('获取方案定制详情失败:', err);
            return res.json({
                status: 'error',
                message: '获取数据失败'
            });
        }
        
        if (results.length === 0) {
            return res.json({
                status: 'error',
                message: '记录不存在'
            });
        }
        
        res.json({
            status: 'ok',
            data: results[0]
        });
    });
});
// 删除方案定制记录
app.post('/apis/business_customization/delete', (req, res) => {
    const id = parseInt(req.body.id);
    
    if (!id) {
        return res.json({
            status: 'error',
            message: '参数错误'
        });
    }
    
    const sql = 'DELETE FROM business_customization WHERE id = ?';
    
    db.query(sql, [id], (err, result) => {
        if (err) {
            console.error('删除方案定制记录失败:', err);
            return res.json({
                status: 'error',
                message: '删除失败'
            });
        }
        
        if (result.affectedRows === 0) {
            return res.json({
                status: 'error',
                message: '记录不存在'
            });
        }
        
        res.json({
            status: 'ok',
            message: '删除成功'
        });
    });
});

// 注册微信API路由
wechatApi.registerWechatRoutes(app);

// ==================== 微信文章管理API ====================

// 获取微信文章列表
app.get('/apis/wechat_articles/', (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.size) || 10;
        const offset = (page - 1) * pageSize;

        // 构建查询条件
        let whereClause = 'WHERE 1=1';
        let queryParams = [];

        // 按父文章ID筛选
        if (req.query.parent_article_id) {
            whereClause += ' AND parent_article_id = ?';
            queryParams.push(req.query.parent_article_id);
        }

        // 按标题搜索
        if (req.query.title) {
            whereClause += ' AND title LIKE ?';
            queryParams.push(`%${req.query.title}%`);
        }

        // 按内容搜索
        if (req.query.content) {
            whereClause += ' AND content_text LIKE ?';
            queryParams.push(`%${req.query.content}%`);
        }

        // 获取总数
        const countSql = `SELECT COUNT(*) as total FROM wechat_articles ${whereClause}`;
        db.query(countSql, queryParams, (err, countResult) => {
            if (err) {
                console.error('查询微信文章总数失败:', err);
                return res.json({ status: 'error', msg: '查询失败' });
            }

            const total = countResult[0].total;

            // 获取分页数据
            const dataSql = `
                SELECT id, article_id, title, digest, content_text, image_urls,
                       section_order, parent_article_id, created_at, updated_at
                FROM wechat_articles
                ${whereClause}
                ORDER BY parent_article_id, section_order, created_at DESC
                LIMIT ? OFFSET ?
            `;

            db.query(dataSql, [...queryParams, pageSize, offset], (err, results) => {
                if (err) {
                    console.error('查询微信文章列表失败:', err);
                    return res.json({ status: 'error', msg: '查询失败' });
                }

                // 处理image_urls JSON字段
                const processedResults = results.map(item => ({
                    ...item,
                    image_urls: typeof item.image_urls === 'string' ? JSON.parse(item.image_urls || '[]') : item.image_urls || [],
                    content_preview: item.content_text ? item.content_text.substring(0, 100) + '...' : ''
                }));

                res.json({
                    status: 'ok',
                    data: processedResults,
                    total: total,
                    page: page,
                    page_size: pageSize,
                    total_pages: Math.ceil(total / pageSize)
                });
            });
        });
    } catch (error) {
        console.error('获取微信文章列表错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 获取单个微信文章详情
app.get('/apis/wechat_article_detail/', (req, res) => {
    try {
        const articleId = req.query.article_id;
        if (!articleId) {
            return res.json({ status: 'error', msg: '缺少文章ID' });
        }

        const sql = 'SELECT * FROM wechat_articles WHERE article_id = ? ORDER BY section_order';
        db.query(sql, [articleId], (err, results) => {
            if (err) {
                console.error('查询微信文章详情失败:', err);
                return res.json({ status: 'error', msg: '查询失败' });
            }

            if (results.length === 0) {
                return res.json({ status: 'error', msg: '文章不存在' });
            }

            // 处理image_urls JSON字段
            const processedResults = results.map(item => ({
                ...item,
                image_urls: typeof item.image_urls === 'string' ? JSON.parse(item.image_urls || '[]') : item.image_urls || []
            }));

            res.json({
                status: 'ok',
                data: processedResults
            });
        });
    } catch (error) {
        console.error('获取微信文章详情错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 通过文章标题获取微信文章详情
app.get('/apis/wechat_article_by_title/', (req, res) => {
    try {
        const title = req.query.title;
        if (!title) {
            return res.json({ status: 'error', msg: '缺少文章标题' });
        }

        const sql = 'SELECT * FROM wechat_articles WHERE title = ? ORDER BY section_order';
        db.query(sql, [title], (err, results) => {
            if (err) {
                console.error('通过标题查询微信文章失败:', err);
                return res.json({ status: 'error', msg: '查询失败' });
            }

            if (results.length === 0) {
                return res.json({ status: 'error', msg: '未找到标题为"' + title + '"的文章' });
            }

            // 处理image_urls JSON字段
            const processedResults = results.map(item => ({
                ...item,
                image_urls: typeof item.image_urls === 'string' ? JSON.parse(item.image_urls || '[]') : item.image_urls || []
            }));

            res.json({
                status: 'ok',
                data: processedResults
            });
        });
    } catch (error) {
        console.error('通过标题获取微信文章错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 获取微信文章统计信息
app.get('/apis/wechat_articles_stats/', (req, res) => {
    try {
        const sql = `
            SELECT
                COUNT(*) as total_sections,
                COUNT(DISTINCT parent_article_id) as total_articles,
                COUNT(CASE WHEN image_urls != '[]' AND image_urls IS NOT NULL THEN 1 END) as sections_with_images,
                AVG(CHAR_LENGTH(content_text)) as avg_content_length
            FROM wechat_articles
        `;

        db.query(sql, (err, results) => {
            if (err) {
                console.error('查询微信文章统计失败:', err);
                return res.json({ status: 'error', msg: '查询失败' });
            }

            res.json({
                status: 'ok',
                data: results[0]
            });
        });
    } catch (error) {
        console.error('获取微信文章统计错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 根据微信文章内容生成产品详情页面
app.post('/apis/generate_product_from_wechat/', (req, res) => {
    try {
        const { article_id, product_title, product_type = '微信文章', lang = 0 } = req.body;

        if (!article_id) {
            return res.json({ status: 'error', msg: '缺少文章ID' });
        }

        // 获取微信文章内容
        const sql = 'SELECT * FROM wechat_articles WHERE article_id = ? ORDER BY section_order';
        db.query(sql, [article_id], (err, results) => {
            if (err) {
                console.error('查询微信文章失败:', err);
                return res.json({ status: 'error', msg: '查询微信文章失败' });
            }

            if (results.length === 0) {
                return res.json({ status: 'error', msg: '微信文章不存在' });
            }

            // 组合文章内容
            let htmlContent = '';
            let allImages = [];

            results.forEach(section => {
                if (section.content_text && section.content_text.trim()) {
                    htmlContent += `<p>${section.content_text}</p>\n`;
                }

                if (section.image_urls) {
                    const images = typeof section.image_urls === 'string' ?
                        JSON.parse(section.image_urls || '[]') : section.image_urls || [];

                    images.forEach(imageUrl => {
                        htmlContent += `<img src="${imageUrl}" style="width: 100%; height: auto;" />\n`;
                        allImages.push(imageUrl);
                    });
                }
            });

            // 创建产品记录
            const title = product_title || results[0].title || '微信文章产品';
            const digest = results[0].digest || '';
            const firstImage = allImages.length > 0 ? allImages[0] : null;

            const insertSql = `
                INSERT INTO products (name, description, info_type, lang, image, html_content, display_order, \`show\`)
                VALUES (?, ?, ?, ?, ?, ?, 100, 1)
            `;

            db.query(insertSql, [title, digest, product_type, lang, firstImage, htmlContent], (err, result) => {
                if (err) {
                    console.error('创建产品失败:', err);
                    return res.json({ status: 'error', msg: '创建产品失败' });
                }

                res.json({
                    status: 'ok',
                    msg: '产品创建成功',
                    product_id: result.insertId,
                    sections_count: results.length,
                    images_count: allImages.length
                });
            });
        });
    } catch (error) {
        console.error('生成产品错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 获取微信文章内容（用于编辑器）
app.post('/apis/get_wechat_article/', (req, res) => {
    try {
        const { filters, fileds } = req.body;

        if (!filters || !filters.article_id) {
            return res.json({ status: 'error', msg: '缺少文章ID' });
        }

        const articleId = filters.article_id;
        const sql = 'SELECT * FROM wechat_articles WHERE article_id = ? ORDER BY section_order';

        db.query(sql, [articleId], (err, results) => {
            if (err) {
                console.error('查询微信文章失败:', err);
                return res.json({ status: 'error', msg: '查询失败' });
            }

            if (results.length === 0) {
                return res.json({ status: 'error', msg: '文章不存在' });
            }

            // 合并所有段落内容 - 优先使用保存的HTML内容
            let htmlContent = '';
            let titleAdded = false;

            results.forEach((section, index) => {
                // 只在第一个段落添加标题（如果有标题的话）
                if (!titleAdded && section.title && section.title.trim()) {
                    htmlContent += `<h2>${section.title}</h2>`;
                    titleAdded = true;
                }

                // 优先使用保存的HTML内容，如果没有则回退到重新构建
                if (section.html_content && section.html_content.trim()) {
                    htmlContent += section.html_content;
                } else {
                    // 回退逻辑：重新构建HTML内容
                    // 添加文字内容
                    if (section.content_text && section.content_text.trim()) {
                        htmlContent += `<p>${section.content_text}</p>`;
                    }

                    // 添加图片
                    if (section.image_urls) {
                        const images = typeof section.image_urls === 'string' ?
                            JSON.parse(section.image_urls || '[]') : section.image_urls || [];

                        images.forEach(imageUrl => {
                            htmlContent += `<img src="${imageUrl}" style="width: 100%; height: auto; margin: 10px 0;" />`;
                        });
                    }
                }
            });

            res.json({
                status: 'ok',
                data: [{
                    article_id: articleId,
                    title: results[0].title,
                    html_content: htmlContent,
                    sections_count: results.length,
                    created_at: results[0].created_at
                }]
            });
        });
    } catch (error) {
        console.error('获取微信文章内容错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 通过文章标题获取微信文章内容（用于编辑器）
app.post('/apis/get_wechat_article_by_title/', (req, res) => {
    try {
        const { title } = req.body;

        if (!title) {
            return res.json({ status: 'error', msg: '缺少文章标题' });
        }

        const sql = 'SELECT * FROM wechat_articles WHERE title = ? ORDER BY section_order';

        db.query(sql, [title], (err, results) => {
            if (err) {
                console.error('通过标题查询微信文章失败:', err);
                return res.json({ status: 'error', msg: '查询失败' });
            }

            if (results.length === 0) {
                return res.json({ status: 'error', msg: '未找到标题为"' + title + '"的文章' });
            }

            // 合并所有段落内容 - 优先使用保存的HTML内容
            let htmlContent = '';
            let titleAdded = false;

            results.forEach((section, index) => {
                // 只在第一个段落添加标题（如果有标题的话）
                if (!titleAdded && section.title && section.title.trim()) {
                    htmlContent += `<h2>${section.title}</h2>`;
                    titleAdded = true;
                }

                // 优先使用保存的HTML内容，如果没有则回退到重新构建
                if (section.html_content && section.html_content.trim()) {
                    htmlContent += section.html_content;
                } else {
                    // 回退逻辑：重新构建HTML内容
                    // 添加文字内容
                    if (section.content_text && section.content_text.trim()) {
                        htmlContent += `<p>${section.content_text}</p>`;
                    }

                    // 添加图片
                    if (section.image_urls) {
                        const images = typeof section.image_urls === 'string' ?
                            JSON.parse(section.image_urls || '[]') : section.image_urls || [];

                        images.forEach(imageUrl => {
                            htmlContent += `<img src="${imageUrl}" style="width: 100%; height: auto; margin: 10px 0;" />`;
                        });
                    }
                }
            });

            res.json({
                status: 'ok',
                data: [{
                    article_id: results[0].article_id,
                    title: results[0].title,
                    html_content: htmlContent,
                    sections_count: results.length,
                    created_at: results[0].created_at
                }]
            });
        });
    } catch (error) {
        console.error('通过标题获取微信文章内容错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 更新微信文章内容
app.post('/apis/update_wechat_article/', (req, res) => {
    try {
        const { filters, html_content } = req.body;

        if (!filters || !filters.article_id) {
            return res.json({ status: 'error', msg: '缺少文章ID' });
        }

        if (!html_content) {
            return res.json({ status: 'error', msg: '内容不能为空' });
        }

        const articleId = filters.article_id;

        // 新的保存策略：将编辑后的HTML内容重新分段保存
        // 1. 先删除该文章的所有现有段落
        const deleteSql = 'DELETE FROM wechat_articles WHERE article_id = ?';

        db.query(deleteSql, [articleId], (err) => {
            if (err) {
                console.error('删除旧文章段落失败:', err);
                return res.json({ status: 'error', msg: '更新失败' });
            }

            // 2. 重新分段并保存
            const sections = parseHtmlToSections(html_content);
            let insertedCount = 0;
            let hasError = false;

            if (sections.length === 0) {
                return res.json({ status: 'error', msg: '内容解析失败' });
            }

            // 先获取原文章信息（在删除之前）
            const getOriginalSql = 'SELECT title, digest FROM wechat_articles WHERE article_id = ? LIMIT 1';
            db.query(getOriginalSql, [articleId], (err, originalResults) => {
                const originalTitle = originalResults && originalResults.length > 0 ? originalResults[0].title : '编辑后的文章';
                const originalDigest = originalResults && originalResults.length > 0 ? originalResults[0].digest : '';

                // 现在删除旧段落
                const deleteSql = 'DELETE FROM wechat_articles WHERE article_id = ?';

                db.query(deleteSql, [articleId], (err) => {
                    if (err) {
                        console.error('删除旧文章段落失败:', err);
                        return res.json({ status: 'error', msg: '更新失败' });
                    }

                // 3. 插入新的分段内容
                sections.forEach((section, index) => {
                    if (hasError) return;

                    const insertSql = `
                        INSERT INTO wechat_articles (
                            article_id, title, digest, content_text, html_content, image_urls,
                            section_order, parent_article_id, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                    `;

                    const values = [
                        articleId,
                        originalTitle,
                        originalDigest,
                        section.content_text,
                        section.html_content || section.content_text, // 保存HTML内容，如果没有则使用纯文本
                        JSON.stringify(section.image_urls),
                        section.section_order,
                        articleId
                    ];

                    db.query(insertSql, values, (err, result) => {
                        if (err) {
                            console.error('插入文章段落失败:', err);
                            hasError = true;
                            return res.json({ status: 'error', msg: '保存失败' });
                        }

                        insertedCount++;

                        // 所有段落都插入完成
                        if (insertedCount === sections.length) {
                            res.json({
                                status: 'ok',
                                msg: '文章更新成功',
                                sections_count: insertedCount
                            });
                        }
                    });
                });
                });
            });
        });
    } catch (error) {
        console.error('更新微信文章错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 创建新的微信文章
app.post('/apis/create_wechat_article/', (req, res) => {
    try {
        const { article_id, title, digest, content_text, image_urls, section_order, parent_article_id } = req.body;

        // 验证必填字段
        if (!article_id || !title || !content_text) {
            return res.json({ status: 'error', msg: '缺少必填字段：article_id, title, content_text' });
        }

        // 检查文章ID是否已存在
        const checkSql = 'SELECT COUNT(*) as count FROM wechat_articles WHERE article_id = ?';
        db.query(checkSql, [article_id], (err, results) => {
            if (err) {
                console.error('检查文章ID失败:', err);
                return res.json({ status: 'error', msg: '检查文章ID失败' });
            }

            if (results[0].count > 0) {
                return res.json({ status: 'error', msg: '文章ID已存在，请重试' });
            }

            // 插入新文章
            const insertSql = `
                INSERT INTO wechat_articles (
                    article_id, title, digest, content_text, image_urls,
                    section_order, parent_article_id, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            `;

            const values = [
                article_id,
                title,
                digest || '',
                content_text,
                image_urls || '[]',
                section_order || 0,
                parent_article_id || article_id
            ];

            db.query(insertSql, values, (err, result) => {
                if (err) {
                    console.error('创建文章失败:', err);
                    return res.json({ status: 'error', msg: '创建文章失败' });
                }

                res.json({
                    status: 'ok',
                    msg: '文章创建成功',
                    article_id: article_id,
                    title: title,
                    insert_id: result.insertId
                });
            });
        });
    } catch (error) {
        console.error('创建微信文章错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 通过文章标题更新微信文章内容
app.post('/apis/update_wechat_article_by_title/', (req, res) => {
    try {
        const { title, html_content } = req.body;

        if (!title) {
            return res.json({ status: 'error', msg: '缺少文章标题' });
        }

        if (!html_content) {
            return res.json({ status: 'error', msg: '内容不能为空' });
        }

        // 首先查找文章ID和其他信息
        const findSql = 'SELECT article_id, digest FROM wechat_articles WHERE title = ? LIMIT 1';

        db.query(findSql, [title], (err, results) => {
            if (err) {
                console.error('查找文章失败:', err);
                return res.json({ status: 'error', msg: '查找文章失败' });
            }

            if (results.length === 0) {
                return res.json({ status: 'error', msg: '未找到标题为"' + title + '"的文章' });
            }

            const articleId = results[0].article_id;
            const originalDigest = results[0].digest || '';

            // 新的保存策略：将编辑后的HTML内容重新分段保存
            // 1. 先删除该文章的所有现有段落
            const deleteSql = 'DELETE FROM wechat_articles WHERE title = ?';

            db.query(deleteSql, [title], (err) => {
                if (err) {
                    console.error('删除旧文章段落失败:', err);
                    return res.json({ status: 'error', msg: '更新失败' });
                }

                // 2. 重新分段并保存
                const sections = parseHtmlToSections(html_content);
                let insertedCount = 0;
                let hasError = false;

                if (sections.length === 0) {
                    return res.json({ status: 'error', msg: '内容解析失败' });
                }

                // 3. 插入新的分段内容
                sections.forEach((section, index) => {
                    if (hasError) return;

                    const insertSql = `
                        INSERT INTO wechat_articles (
                            article_id, title, digest, content_text, html_content, image_urls,
                            section_order, parent_article_id, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                    `;

                    const values = [
                        articleId,
                        title,
                        originalDigest,
                        section.content_text,
                        section.html_content || section.content_text, // 保存HTML内容，如果没有则使用纯文本
                        JSON.stringify(section.image_urls),
                        section.section_order,
                        articleId
                    ];

                    db.query(insertSql, values, (err, result) => {
                        if (err) {
                            console.error('插入文章段落失败:', err);
                            hasError = true;
                            return res.json({ status: 'error', msg: '保存失败' });
                        }

                        insertedCount++;

                        // 所有段落都插入完成
                        if (insertedCount === sections.length) {
                            res.json({
                                status: 'ok',
                                msg: '文章内容更新成功',
                                article_id: articleId,
                                title: title,
                                sections_count: insertedCount
                            });
                        }
                    });
                });
            });
        });
    } catch (error) {
        console.error('通过标题更新微信文章错误:', error);
        res.json({ status: 'error', msg: '服务器内部错误' });
    }
});

// 简单的中英文翻译函数（基于关键词映射）
function translateText(text) {
    const translations = {
        '新起点、新辉煌——热烈祝贺公司乔迁大吉！': 'New Beginning, New Glory - Warm Congratulations on Company Relocation!',
        '一、乔迁盛典': 'I. Relocation Ceremony',
        '二、新址介绍': 'II. New Address Introduction',
        '三、未来展望': 'III. Future Prospects',
        '公司': 'Company',
        '乔迁': 'Relocation',
        '新起点': 'New Beginning',
        '新辉煌': 'New Glory',
        '热烈祝贺': 'Warm Congratulations',
        '大吉': 'Good Fortune',
        '盛典': 'Ceremony',
        '介绍': 'Introduction',
        '展望': 'Prospects',
        '未来': 'Future'
    };

    let translatedText = text;
    for (const [chinese, english] of Object.entries(translations)) {
        translatedText = translatedText.replace(new RegExp(chinese, 'g'), english);
    }

    return translatedText;
}

// 翻译HTML内容
function translateContent(htmlContent) {
    const translations = {
        '2023年8月26日上午十点整': 'August 26, 2023, 10:00 AM',
        '厦门贝启科技有限公司': 'Xiamen Bearkey Technology Co., Ltd.',
        '乔迁庆典': 'Relocation Celebration',
        '隆重举行': 'Grandly Held',
        '新办公地址': 'New Office Address',
        '位于': 'Located at',
        '交通便利': 'Convenient Transportation',
        '环境优美': 'Beautiful Environment',
        '设施完善': 'Complete Facilities',
        '为公司发展': 'For Company Development',
        '提供了': 'Provides',
        '更好的': 'Better',
        '条件': 'Conditions',
        '我们将': 'We will',
        '继续': 'Continue',
        '努力': 'Work Hard',
        '为客户': 'For Customers',
        '提供': 'Provide',
        '优质': 'High-quality',
        '服务': 'Service',
        '产品': 'Products'
    };

    let translatedContent = htmlContent;
    for (const [chinese, english] of Object.entries(translations)) {
        translatedContent = translatedContent.replace(new RegExp(chinese, 'g'), english);
    }

    return translatedContent;
}

// 完整翻译HTML内容 - 避免中英文混杂
function translateContentCompletely(htmlContent) {
    try {
        console.log('开始完整翻译HTML内容...');
        console.log('原始内容长度:', htmlContent.length);

        let translatedHtml = htmlContent;

        // 完整段落翻译映射 - 包含多种可能的格式
        const completeTranslations = {
            // 处理HTML实体字符的版本
            '2023年8月26日上午十点整，厦门贝启迎来八周年发展历程中的又一个重要里程碑&mdash;&mdash;正式入驻星网锐捷海西科技园，新址位于-福建省福州市高新区星网锐捷海西科技园1号楼东101单元。乔迁同时也预示着公司又一个全新的开端。 贝启科技刘总、陈总、罗总以及星网锐捷、瑞芯微、润和软件、德明通讯、福建省开源数字技术研究院等领导共同参加了此次活动。':
            'On August 26, 2023, at 10:00 AM, Xiamen Bearkey reached another important milestone in its eight-year development journey - officially settling into Star-Net Ruijie Haixi Technology Park. The new address is located at Unit 101, East Building 1, Star-Net Ruijie Haixi Technology Park, High-tech Zone, Fuzhou, Fujian Province. The relocation also signifies another brand new beginning for the company. General Manager Liu, General Manager Chen, General Manager Luo of Bearkey Technology, along with leaders from Star-Net Ruijie, Rockchip, HopeRun Software, Deming Communications, Fujian Open Source Digital Technology Research Institute and others jointly participated in this event.',

            // 处理普通破折号的版本
            '2023年8月26日上午十点整，厦门贝启迎来八周年发展历程中的又一个重要里程碑——正式入驻星网锐捷海西科技园，新址位于-福建省福州市高新区星网锐捷海西科技园1号楼东101单元。乔迁同时也预示着公司又一个全新的开端。 贝启科技刘总、陈总、罗总以及星网锐捷、瑞芯微、润和软件、德明通讯、福建省开源数字技术研究院等领导共同参加了此次活动。':
            'On August 26, 2023, at 10:00 AM, Xiamen Bearkey reached another important milestone in its eight-year development journey - officially settling into Star-Net Ruijie Haixi Technology Park. The new address is located at Unit 101, East Building 1, Star-Net Ruijie Haixi Technology Park, High-tech Zone, Fuzhou, Fujian Province. The relocation also signifies another brand new beginning for the company. General Manager Liu, General Manager Chen, General Manager Luo of Bearkey Technology, along with leaders from Star-Net Ruijie, Rockchip, HopeRun Software, Deming Communications, Fujian Open Source Digital Technology Research Institute and others jointly participated in this event.',

            '公司领导与莅临的领导及嘉宾们在大家的掌声和礼炮声中揭下红幕，标志着厦门贝启科技有限公司新办公室即日起正式启用':
            'Company leaders and visiting leaders and guests unveiled the red curtain amid applause and salutes, marking the official opening of Xiamen Bearkey Technology Co., Ltd.\'s new office from today.',

            '星网锐捷海西科技园位于福建省福州市高新区，是中国500强聚集地，周边三公里云集众多高新科技上下游企业。便捷的上下游区位，将助力贝启科技继续腾跃至更高的企业战略目标。':
            'Star-Net Ruijie Haixi Technology Park is located in the High-tech Zone of Fuzhou, Fujian Province. It is a gathering place for China\'s top 500 companies, with numerous high-tech upstream and downstream enterprises within a 3-kilometer radius. The convenient upstream and downstream location will help Bearkey Technology continue to leap to higher corporate strategic goals.',

            '厦门贝启科技有限公司成立于2015年8月26日，是一家专注于开源软硬件设计的高科技企业；拥有授权知识产权38项、企业认定2项、技术认定1项、荣誉称号3项,并于2022年成为福建省开源数字技术研究院理事单位成员 。同时是96Boards硬件开源社区MP领导者合作伙伴，与（瑞芯微）、Linaro开源软件组织、ARM中国、Toybrick社区合作紧密。':
            'Xiamen Bearkey Technology Co., Ltd. was established on August 26, 2015. It is a high-tech enterprise focused on open source hardware and software design. The company owns 38 authorized intellectual property rights, 2 enterprise certifications, 1 technical certification, and 3 honorary titles. In 2022, it became a council member of Fujian Open Source Digital Technology Research Institute. It is also a MP leader partner of the 96Boards hardware open source community, and has close cooperation with Rockchip, Linaro open source software organization, ARM China, and Toybrick community.',

            '作为国内领先的嵌入式产品平台提供商，贝启科技始终致力于打造高品质核心板，已成功帮助超过500+家工业客户完成产品的快速开发与上市。':
            'As a leading domestic embedded product platform provider, Bearkey Technology has always been committed to creating high-quality core boards and has successfully helped over 500 industrial customers complete rapid product development and market launch.',

            '近年来，公司紧密跟进国产化软硬件产品趋势，作为OpenHarmony项目群生态委员会教育专委会成员、医疗健康专委会成员，拥有全国产化硬件产品，我们积极适配OpenHarmony等国产化操作系统。拥有的RK3588/RK3568/RK3566行业主板/开发板均已适配支持OpenHarmony系统，已量产出货。':
            'In recent years, the company has closely followed the trend of domestically produced hardware and software products. As a member of the Education Committee and Medical Health Committee of the OpenHarmony Project Ecosystem Committee, with fully domestically produced hardware products, we actively adapt to domestic operating systems such as OpenHarmony. Our RK3588/RK3568/RK3566 industry motherboards/development boards have all been adapted to support the OpenHarmony system and are already in mass production and delivery.',

            '此次乔迁，寓意着公司未来美好的发展与壮大。2023年，厦门贝启科技有限公司也将翻开崭新的一页，踏上新的征程！':
            'This relocation symbolizes the company\'s beautiful future development and growth. In 2023, Xiamen Bearkey Technology Co., Ltd. will also turn a new page and embark on a new journey!',

            // 标题翻译
            '一、乔迁盛典': 'I. Relocation Ceremony',
            '二、乔迁盛典现场图片': 'II. Relocation Ceremony Site Photos',
            '三、高新科技企业核心聚集地': 'III. Core Hub of High-tech Enterprises',
            '四、坚持深耕行业8年': 'IV. 8 Years of Deep Industry Cultivation',
            '五、核心产品展示': 'V. Core Product Display'
        };

        // 按长度排序，先替换长的内容，避免部分匹配
        const sortedTranslations = Object.entries(completeTranslations).sort((a, b) => b[0].length - a[0].length);

        let translationCount = 0;
        for (const [chinese, english] of sortedTranslations) {
            const beforeLength = translatedHtml.length;
            // 使用split和join方法进行精确替换，避免正则表达式的问题
            translatedHtml = translatedHtml.split(chinese).join(english);
            const afterLength = translatedHtml.length;

            if (beforeLength !== afterLength) {
                translationCount++;
                console.log(`翻译成功 ${translationCount}: ${chinese.substring(0, 50)}...`);
            }
        }

        // 如果还有未翻译的中文内容，使用正则表达式进行HTML标签内文本的翻译
        if (translatedHtml.match(/[\u4e00-\u9fa5]/)) {
            console.log('检测到未翻译的中文内容，进行HTML标签内文本翻译...');
            translatedHtml = translateHtmlTagContent(translatedHtml, completeTranslations);
        }

        console.log(`完整翻译完成，共翻译 ${translationCount} 个段落`);
        return translatedHtml;
    } catch (error) {
        console.error('翻译失败:', error);
        return htmlContent;
    }
}

// 翻译HTML标签内的中文文本
function translateHtmlTagContent(htmlContent, translations) {
    try {
        let result = htmlContent;

        // 提取所有翻译映射的中文文本，按长度排序
        const chineseTexts = Object.keys(translations).sort((a, b) => b.length - a.length);

        for (const chineseText of chineseTexts) {
            const englishText = translations[chineseText];

            // 使用正则表达式匹配HTML标签内的中文文本
            // 匹配 <span>中文内容</span> 格式
            const spanRegex = new RegExp(`(<span[^>]*>)([^<]*${chineseText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[^<]*)(</span>)`, 'g');
            result = result.replace(spanRegex, (_, openTag, content, closeTag) => {
                const translatedContent = content.replace(chineseText, englishText);
                console.log(`HTML标签内翻译: ${content.substring(0, 50)}... -> ${translatedContent.substring(0, 50)}...`);
                return openTag + translatedContent + closeTag;
            });

            // 匹配 <p>中文内容</p> 格式
            const pRegex = new RegExp(`(<p[^>]*>)([^<]*${chineseText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[^<]*)(</p>)`, 'g');
            result = result.replace(pRegex, (_, openTag, content, closeTag) => {
                const translatedContent = content.replace(chineseText, englishText);
                console.log(`P标签内翻译: ${content.substring(0, 50)}... -> ${translatedContent.substring(0, 50)}...`);
                return openTag + translatedContent + closeTag;
            });
        }

        return result;
    } catch (error) {
        console.error('HTML标签内文本翻译失败:', error);
        return htmlContent;
    }
}

// 使用翻译API翻译HTML内容（按优先级：Google翻译 → 百度翻译 → 其他备用）
async function translateHtmlContentWithGoogle(htmlContent) {
    try {
        console.log('开始翻译HTML内容（优先Google翻译）...');

        // 简单的文本提取和翻译方法
        const textRegex = />([^<]*[\u4e00-\u9fa5][^<]*)</g;
        const textsToTranslate = [];
        let match;

        // 提取所有包含中文的文本
        while ((match = textRegex.exec(htmlContent)) !== null) {
            const text = match[1].trim();
            if (text && !textsToTranslate.includes(text)) {
                textsToTranslate.push(text);
            }
        }

        console.log(`需要翻译 ${textsToTranslate.length} 个文本片段`);

        let translatedHtml = htmlContent;

        // 逐个翻译文本片段
        for (const text of textsToTranslate) {
            try {
                const fetch = (await import('node-fetch')).default;
                const response = await fetch('http://localhost:3002/api/translate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: text,
                        from: 'zh',
                        to: 'en'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.translatedText) {
                        // 安全的替换逻辑 - 允许翻译错误，不会导致语法错误
                        if (translatedHtml.includes(text)) {
                            try {
                                // 直接替换，不做复杂的转义处理
                                translatedHtml = translatedHtml.split(text).join(data.translatedText);
                            } catch (error) {
                                // 如果替换失败，跳过这个翻译，继续处理其他文本
                                console.warn(`替换文本失败，跳过: ${text.substring(0, 30)}...`);
                            }
                        }
                        console.log(`翻译成功: ${text.substring(0, 30)}... -> ${data.translatedText.substring(0, 30)}...`);
                    } else {
                        console.warn(`翻译失败: ${text.substring(0, 30)}...`);
                    }
                } else {
                    console.warn(`翻译请求失败: ${text.substring(0, 30)}...`);
                }

                // 添加延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 200));
            } catch (error) {
                console.error(`翻译单个文本失败: ${text.substring(0, 30)}...`, error);
            }
        }

        console.log('HTML内容翻译完成');
        return translatedHtml;

    } catch (error) {
        console.error('翻译HTML内容失败:', error);
        return htmlContent; // 返回原始内容
    }
}

// 使用翻译API翻译文本（按优先级：Google翻译 → 百度翻译 → 其他备用）
async function translateTextWithGoogle(text) {
    try {
        const fetch = (await import('node-fetch')).default;
        const response = await fetch('http://localhost:3002/api/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: text,
                from: 'zh',
                to: 'en'
            })
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success && data.translatedText) {
                console.log(`标题翻译成功: ${text} -> ${data.translatedText}`);
                return data.translatedText;
            }
        }

        console.warn(`标题翻译失败，使用原文: ${text}`);
        return text;
    } catch (error) {
        console.error('翻译标题失败:', error);
        return text;
    }
}

// 生成微信文章详情页
app.post('/apis/generate_wechat_article_page/', async (req, res) => {
    try {
        const { title, article_id } = req.body;

        if (!title && !article_id) {
            return res.json({
                status: 'error',
                msg: '缺少文章标题或ID'
            });
        }

        // 构建查询条件
        let sql, params;
        if (title) {
            sql = 'SELECT * FROM wechat_articles WHERE title = ? ORDER BY section_order';
            params = [title];
        } else {
            sql = 'SELECT * FROM wechat_articles WHERE article_id = ? ORDER BY section_order';
            params = [article_id];
        }

        db.query(sql, params, async (err, results) => {
            if (err) {
                console.error('查询微信文章失败:', err);
                return res.json({ status: 'error', msg: '查询文章失败' });
            }

            if (results.length === 0) {
                return res.json({ status: 'error', msg: '文章不存在' });
            }

            try {
                // 合并所有段落内容
                let htmlContent = '';
                const articleTitle = results[0].title || '微信文章';

                results.forEach((section) => {
                    // 优先使用保存的HTML内容，如果没有则回退到重新构建
                    if (section.html_content && section.html_content.trim()) {
                        htmlContent += section.html_content;
                    } else {
                        // 回退逻辑：重新构建HTML内容
                        if (section.content_text && section.content_text.trim()) {
                            htmlContent += `<p>${section.content_text}</p>`;
                        }

                        // 添加图片
                        if (section.image_urls) {
                            const images = typeof section.image_urls === 'string' ?
                                JSON.parse(section.image_urls || '[]') : section.image_urls || [];

                            images.forEach(imageUrl => {
                                htmlContent += `<img src="${imageUrl}" style="width: 100%; height: auto; margin: 10px 0;" />`;
                            });
                        }
                    }
                });

                // 生成文件名（去除特殊字符）
                const safeTitle = articleTitle.replace(/[^\w\u4e00-\u9fa5]/g, '');
                const fileNameCN = safeTitle;
                const fileNameEN = safeTitle + '_en';

                // 确保目录存在
                const articleDir = path.join(__dirname, 'public', 'article');
                if (!fs.existsSync(articleDir)) {
                    fs.mkdirSync(articleDir, { recursive: true });
                }

                // 第一步：生成中文页面模板
                const cnTemplate = generateArticleTemplate(articleTitle, htmlContent, 'zh-CN');
                const cnFilePath = path.join(articleDir, `${fileNameCN}.html`);
                fs.writeFileSync(cnFilePath, cnTemplate, 'utf8');
                console.log('中文页面生成完成:', cnFilePath);

                // 第二步：使用翻译API生成英文页面（优先Google翻译）
                console.log('开始翻译生成英文页面（优先Google翻译）...');

                // 翻译标题和内容
                const translatedTitle = await translateTextWithGoogle(articleTitle);
                const translatedContent = await translateHtmlContentWithGoogle(htmlContent);

                // 生成英文页面模板
                const enTemplate = generateArticleTemplate(translatedTitle, translatedContent, 'en');
                const enFilePath = path.join(articleDir, `${fileNameEN}.html`);
                fs.writeFileSync(enFilePath, enTemplate, 'utf8');
                console.log('英文页面生成完成:', enFilePath);

                res.json({
                    status: 'ok',
                    msg: '文章详情页生成成功（中英文版本，使用智能翻译）',
                    files: {
                        chinese: `/article/${fileNameCN}.html`,
                        english: `/article/${fileNameEN}.html`
                    }
                });
            } catch (translationError) {
                console.error('翻译过程中出错:', translationError);
                res.json({
                    status: 'error',
                    msg: '翻译失败: ' + translationError.message
                });
            }
        });
    } catch (error) {
        console.error('生成文章详情页失败:', error);
        res.json({
            status: 'error',
            msg: '生成页面失败: ' + error.message
        });
    }
});

// 生成文章页面模板
function generateArticleTemplate(title, content, lang) {
    const isEnglish = lang === 'en';
    const pageTitle = isEnglish ? `${title} - Bearkey Technology` : `${title} - 厦门贝启科技有限公司`;
    const bodyClass = isEnglish ? 'english-version' : 'chinese-version';

    return `<!DOCTYPE html>
<html lang="${lang}" style="height:100%">
<head>
    <meta charset="utf-8">
    <title>${pageTitle}</title>
    <meta name="keywords" content="${title}">
    <link rel="icon" href="../images/home/<USER>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="description" content="${title}"/>
    <meta name="author" content=""/>
    <!-- css -->
    <link rel="stylesheet" href="../simple-line-icons/css/simple-line-icons.css">
    <link href="../css/bootstrap.min.css" rel="stylesheet"/>
    <link href="../css/fancybox/jquery.fancybox.css" rel="stylesheet">
    <link href="../css/flexslider.css" rel="stylesheet"/>
    <link href="../css/magnific-popup.css" rel="stylesheet">
    <link href="../css/style.css" rel="stylesheet"/>
    <link href="/css/nav-style.css" rel="stylesheet"/>
    <link href="../css/toolbar-custom.css" rel="stylesheet"/>
    <!-- Font Awesome 图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?0558c2b79797ac39f3317053c376aaa2";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
    <style>
        .article-content {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .article-content img {
            max-width: 100%;
            height: auto;
            margin: 10px 0;
        }
        .article-content h1, .article-content h2, .article-content h3 {
            margin: 20px 0 10px 0;
        }
        .article-content p {
            margin: 10px 0;
        }
    </style>
</head>
<body style="height:100%;overflow-x:hidden;" class="${bodyClass}">
<div id="wrapper" style="height:100%">

    <!-- start header -->
    <header>
        <!-- 导航栏将通过nav.js动态加载 -->
    </header><!-- end header -->

    <section id="content" class="article_detail_cls">
        <div>
            <div class="row" style="margin-bottom: 0px;margin-top: -5em;">
            <hr/>
                <div>
                    <section id="article_info" style="display: block">
                        <div id="article_detail" class="article-content">
                            <h1>${title}</h1>
                            ${content}
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </section>

</div>
<a href="#" class="scrollup"><i class="fa fa-angle-up active"></i></a>

<!-- javascript
    ================================================== -->
<!-- Placed at the end of the document so the pages load faster -->
<script src="../js/jquery.js"></script>
<script src="../js/jquery.easing.1.3.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script src="../js/jquery.fancybox.pack.js"></script>
<script src="../js/jquery.fancybox-media.js"></script>
<script src="../js/portfolio/jquery.quicksand.js"></script>
<script src="../js/portfolio/setting.js"></script>
<script src="../js/jquery.flexslider.js"></script>
<script src="../js/jquery.isotope.min.js"></script>
<script src="../js/jquery.magnific-popup.min.js"></script>
<script src="../js/animate.js"></script>
<script src="../js/custom.js"></script>
<script src="../js/tools.js"></script>
<script src="../js/nav.js"></script>
<script>window.FOOTER_MANUAL_INIT = true;</script>
<script src="../js/footer.js"></script>
<script src="../js/floating-toolbar.js"></script>
<script src="../js/toolbar-data.js"></script>
<script>
// 获取当前文章标题
var articleTitle = ${JSON.stringify(title)};

$(document).ready(function() {
    loadFooter();
});

function chg_lang() {
    const currentPath = window.location.pathname;
    if (currentPath.includes('_en.html')) {
        // 当前是英文页面，切换到中文
        window.location.href = currentPath.replace('_en.html', '.html');
    } else {
        // 当前是中文页面，切换到英文
        window.location.href = currentPath.replace('.html', '_en.html');
    }
}

function loadFooter() {
    console.log('🚀 开始加载页脚...');

    // 找到文章内容区域
    var insertTarget = $('#article_detail');
    console.log('📍 将在文章内容区域后插入页脚');

    // 如果页脚已存在，先移除
    if ($('footer').length > 0) {
        console.log('🗑️ 移除现有页脚');
        $('footer').remove();
    }

    // 在正确位置插入页脚
    insertTarget.after('<footer></footer>');
    console.log('📌 页脚已插入到正确位置');

    var showFooter = function() {
        if (typeof window.initFooter === 'function') {
            console.log('📝 开始初始化页脚内容...');
            window.initFooter();
            console.log('✨ 页脚内容加载完成');
        }
    };

    if (!window.initFooter) {
        console.log('📦 开始加载页脚脚本...');
        var script = document.createElement('script');
        script.src = '../js/footer.js';
        script.onload = showFooter;
        document.body.appendChild(script);
    } else {
        showFooter();
    }
}
</script>
</body>
</html>`;
}

// 微信图片代理接口
app.get('/apis/wechat_image_proxy/', async (req, res) => {
    try {
        const imageUrl = req.query.url;

        if (!imageUrl) {
            return res.status(400).json({ error: '缺少图片URL参数' });
        }

        // 检查是否是微信图片URL
        if (!imageUrl.includes('mmbiz.qpic.cn')) {
            return res.status(400).json({ error: '只支持微信公众号图片' });
        }

        const https = require('https');
        const http = require('http');

        // 根据URL协议选择模块
        const client = imageUrl.startsWith('https') ? https : http;

        // 设置请求头，模拟微信浏览器
        const options = {
            headers: {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.0(0x18000029) NetType/WIFI Language/zh_CN',
                'Referer': 'https://mp.weixin.qq.com/',
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache'
            }
        };

        client.get(imageUrl, options, (response) => {
            // 检查响应状态
            if (response.statusCode !== 200) {
                return res.status(response.statusCode).json({
                    error: `图片获取失败，状态码: ${response.statusCode}`
                });
            }

            // 设置响应头
            res.setHeader('Content-Type', response.headers['content-type'] || 'image/jpeg');
            res.setHeader('Cache-Control', 'public, max-age=86400'); // 缓存1天
            res.setHeader('Access-Control-Allow-Origin', '*');

            // 将图片数据流式传输给客户端
            response.pipe(res);

        }).on('error', (err) => {
            console.error('图片代理错误:', err);
            res.status(500).json({ error: '图片获取失败' });
        });

    } catch (error) {
        console.error('微信图片代理错误:', error);
        res.status(500).json({ error: '服务器内部错误' });
    }
});



// 翻译API实现函数

// 1. MyMemory API (免费，无需API Key)
async function translateWithMyMemoryAPI(text, from, to) {
    try {
        const fetch = (await import('node-fetch')).default;
        const langFrom = from === 'zh' ? 'zh-CN' : from;
        const langTo = to === 'en' ? 'en-US' : to;

        const url = `https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=${langFrom}|${langTo}`;

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            timeout: 8000
        });

        if (response.ok) {
            const data = await response.json();
            if (data.responseData && data.responseData.translatedText) {
                return {
                    success: true,
                    translatedText: data.responseData.translatedText,
                    service: 'mymemory'
                };
            }
        }

        throw new Error('MyMemory API响应异常');
    } catch (error) {
        throw new Error(`MyMemory API失败: ${error.message}`);
    }
}

// 2. LibreTranslate (开源免费翻译服务)
async function translateWithLibreTranslate(text, from, to) {
    try {
        const fetch = (await import('node-fetch')).default;

        // 使用公共LibreTranslate实例
        const response = await fetch('https://libretranslate.de/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                q: text,
                source: from === 'zh' ? 'zh' : from,
                target: to,
                format: 'text'
            }),
            timeout: 8000
        });

        if (response.ok) {
            const data = await response.json();
            if (data.translatedText) {
                return {
                    success: true,
                    translatedText: data.translatedText,
                    service: 'libretranslate'
                };
            }
        }

        throw new Error('LibreTranslate服务响应异常');
    } catch (error) {
        throw new Error(`LibreTranslate翻译失败: ${error.message}`);
    }
}

// 3. Lingva翻译 (免费Google翻译代理)
async function translateWithLingva(text, from, to) {
    try {
        const fetch = (await import('node-fetch')).default;

        // 使用Lingva翻译服务（Google翻译的免费代理）
        const fromLang = from === 'zh' ? 'zh' : from;
        const toLang = to;

        const response = await fetch(`https://lingva.ml/api/v1/${fromLang}/${toLang}/${encodeURIComponent(text)}`, {
            method: 'GET',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            timeout: 8000
        });

        if (response.ok) {
            const data = await response.json();
            if (data.translation) {
                return {
                    success: true,
                    translatedText: data.translation,
                    service: 'lingva'
                };
            }
        }

        throw new Error('Lingva翻译服务响应异常');
    } catch (error) {
        throw new Error(`Lingva翻译失败: ${error.message}`);
    }
}

// 4. 简单翻译服务 (使用多个免费API)
async function translateWithSimpleTranslate(text, from, to) {
    try {
        const fetch = (await import('node-fetch')).default;

        // 尝试使用translate.argosopentech.com
        const response = await fetch('https://translate.argosopentech.com/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                q: text,
                source: from === 'zh' ? 'zh' : from,
                target: to,
                format: 'text'
            }),
            timeout: 8000
        });

        if (response.ok) {
            const data = await response.json();
            if (data.translatedText) {
                return {
                    success: true,
                    translatedText: data.translatedText,
                    service: 'argos-translate'
                };
            }
        }

        throw new Error('Argos翻译服务响应异常');
    } catch (error) {
        throw new Error(`Argos翻译失败: ${error.message}`);
    }
}

// 5. 百度翻译API (备用方案)
async function translateWithBaidu(text, from, to) {
    try {
        const crypto = require('crypto');
        const fetch = (await import('node-fetch')).default;

        // 百度翻译API配置
        const appId = '20250805002424092';
        const secretKey = 'rt3ibsjD6iqv2lhcNBRx';
        const salt = Date.now().toString();
        const query = text;

        // 生成签名
        const signStr = appId + query + salt + secretKey;
        const sign = crypto.createHash('md5').update(signStr).digest('hex');

        // 语言代码转换
        const fromLang = from === 'zh' ? 'zh' : from;
        const toLang = to === 'en' ? 'en' : to;

        const url = 'https://fanyi-api.baidu.com/api/trans/vip/translate';
        const params = new URLSearchParams({
            q: query,
            from: fromLang,
            to: toLang,
            appid: appId,
            salt: salt,
            sign: sign
        });

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params,
            timeout: 10000
        });

        if (response.ok) {
            const data = await response.json();
            if (data.trans_result && data.trans_result.length > 0) {
                const translatedText = data.trans_result.map(item => item.dst).join('\n');
                return {
                    success: true,
                    translatedText: translatedText,
                    service: 'baidu'
                };
            }

            if (data.error_code) {
                throw new Error(`百度翻译错误: ${data.error_code} - ${data.error_msg || '未知错误'}`);
            }
        }

        throw new Error('百度翻译服务响应异常');
    } catch (error) {
        throw new Error(`百度翻译失败: ${error.message}`);
    }
}





// 启动服务器
app.listen(port, () => {
    console.log(`服务器已启动，正在监听端口 ${port}...`);
    console.log(`访问地址: http://localhost:${port}/`);
    console.log('微信公众号接口已集成，请配置AppID和AppSecret后使用');
    console.log('');
    console.log('🌐 翻译服务已启用（按优先级顺序）:');
    console.log('1. MyMemory - 免费翻译API，每天1000次请求');
    console.log('2. LibreTranslate - 开源翻译服务');
    console.log('3. Lingva - Google翻译免费代理');
    console.log('4. Argos Translate - 开源神经机器翻译');
    console.log('5. 百度翻译API - 备用方案（需要API密钥）');
    console.log('');
    console.log('✅ 前4个翻译服务均为免费，百度翻译作为最后备用方案！');
});
